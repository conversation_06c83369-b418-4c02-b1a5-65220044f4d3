import { useState, useEffect, useCallback } from "react";
import { toast } from "sonner";

export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  image: string;
  author: string;
  authorAvatar?: string;
  publishedAt: string;
  readTime: number;
  views: number;
  category: string;
  tags: string[];
  featured: boolean;
  published: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface PostFilters {
  search?: string;
  tag?: string;
  featured?: boolean;
  limit?: number;
  offset?: number;
}

interface UsePostsOptions {
  initialFilters?: PostFilters;
  autoFetch?: boolean;
}

export function usePosts(options: UsePostsOptions = {}) {
  const { initialFilters = {}, autoFetch = true } = options;

  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0,
  });

  const fetchPosts = useCallback(
    async (filters: PostFilters = {}) => {
      setLoading(true);
      setError(null);
      try {
        const params = new URLSearchParams();

        // Add filters to params
        Object.entries({ ...initialFilters, ...filters }).forEach(
          ([key, value]) => {
            if (value !== undefined && value !== null && value !== "") {
              params.append(key, value.toString());
            }
          }
        );

        const response = await fetch(`/api/posts?${params}`);
        const data = await response.json();

        if (response.ok) {
          setPosts(data.posts || []);
          setPagination(data.pagination || pagination);
        } else {
          throw new Error(data.error || "Có lỗi xảy ra khi tải bài viết");
        }
      } catch (error) {
        const errorMessage =
          error instanceof Error
            ? error.message
            : "Có lỗi xảy ra khi tải bài viết";
        setError(errorMessage);
        console.error("Fetch posts error:", error);
      } finally {
        setLoading(false);
      }
    },
    [initialFilters, pagination]
  );

  useEffect(() => {
    if (autoFetch) {
      fetchPosts();
    }
  }, [autoFetch, fetchPosts]);

  const refetch = useCallback(() => {
    fetchPosts();
  }, [fetchPosts]);

  return {
    posts,
    loading,
    error,
    pagination,
    fetchPosts,
    refetch,
  };
}

export function usePost(slug: string) {
  const [post, setPost] = useState<BlogPost | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchPost = useCallback(async () => {
    if (!slug) return;

    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`/api/posts/${slug}`);
      const data = await response.json();

      if (response.ok) {
        setPost(data.post);
      } else {
        throw new Error(data.error || "Không tìm thấy bài viết");
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Có lỗi xảy ra khi tải bài viết";
      setError(errorMessage);
      setPost(null);
    } finally {
      setLoading(false);
    }
  }, [slug]);

  useEffect(() => {
    fetchPost();
  }, [fetchPost]);

  const refetch = useCallback(() => {
    fetchPost();
  }, [fetchPost]);

  return {
    post,
    loading,
    error,
    refetch,
  };
}

export function useFeaturedPosts(limit: number = 3) {
  const { posts, loading, error } = usePosts({
    initialFilters: { featured: true, limit },
    autoFetch: true,
  });

  return {
    posts: posts.slice(0, limit),
    loading,
    error,
  };
}

export function useRecentPosts(limit: number = 5) {
  const { posts, loading, error } = usePosts({
    initialFilters: { limit },
    autoFetch: true,
  });

  return {
    posts: posts.slice(0, limit),
    loading,
    error,
  };
}

export function usePostsByTag(tag: string, limit: number = 10) {
  const { posts, loading, error, fetchPosts } = usePosts({
    initialFilters: { tag, limit },
    autoFetch: true,
  });

  return {
    posts,
    loading,
    error,
    refetch: () => fetchPosts({ tag, limit }),
  };
}
