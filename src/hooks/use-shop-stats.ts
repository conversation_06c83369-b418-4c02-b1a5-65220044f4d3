import { useState, useEffect, useCallback } from "react";
import { toast } from "sonner";

export interface ShopStats {
  totalProducts: number;
  totalCustomers: number;
  averageRating: number;
  totalOrders: number;
  totalRevenue: number;
  deliveryTime: string;
  satisfactionRate: number;
  returnRate: number;
  responseTime: string;
  uptime: number;
}

export interface StatItem {
  icon: React.ReactNode;
  value: string;
  label: string;
  suffix?: string;
  description?: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

interface UseShopStatsOptions {
  autoFetch?: boolean;
  refreshInterval?: number;
}

export function useShopStats(options: UseShopStatsOptions = {}) {
  const { autoFetch = true, refreshInterval } = options;
  
  const [stats, setStats] = useState<ShopStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch("/api/shop/stats");
      const data = await response.json();

      if (response.ok) {
        setStats(data.stats);
      } else {
        throw new Error(data.error || "Có lỗi xảy ra khi tải thống kê");
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Có lỗi xảy ra khi tải thống kê";
      setError(errorMessage);
      console.error("Fetch shop stats error:", error);
      
      // Fallback to mock data if API fails
      setStats(getMockShopStats());
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    if (autoFetch) {
      fetchStats();
    }
  }, [autoFetch, fetchStats]);

  // Auto refresh if interval is set
  useEffect(() => {
    if (refreshInterval && refreshInterval > 0) {
      const interval = setInterval(fetchStats, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [refreshInterval, fetchStats]);

  const refetch = useCallback(() => {
    fetchStats();
  }, [fetchStats]);

  return {
    stats,
    loading,
    error,
    refetch,
  };
}

export function useFormattedShopStats() {
  const { stats, loading, error } = useShopStats();

  const formattedStats: StatItem[] = stats ? [
    {
      icon: null, // Will be set by component
      value: formatNumber(stats.totalProducts),
      label: "Sản phẩm",
      suffix: "+",
      description: "Đa dạng các mặt hàng thời trang",
    },
    {
      icon: null,
      value: formatNumber(stats.totalCustomers),
      label: "Khách hàng hài lòng",
      suffix: "+",
      description: "Tin tưởng và lựa chọn NS Shop",
    },
    {
      icon: null,
      value: stats.averageRating.toFixed(1),
      label: "Đánh giá trung bình",
      suffix: "/5",
      description: "Từ khách hàng đã mua hàng",
    },
    {
      icon: null,
      value: stats.deliveryTime,
      label: "Thời gian giao hàng",
      suffix: "",
      description: "Giao hàng nhanh chóng toàn quốc",
    },
  ] : [];

  return {
    stats: formattedStats,
    loading,
    error,
  };
}

// Helper function to format numbers
function formatNumber(num: number): string {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + "M";
  } else if (num >= 1000) {
    return (num / 1000).toFixed(0) + "K";
  }
  return num.toString();
}

// Mock data fallback
function getMockShopStats(): ShopStats {
  return {
    totalProducts: 10000,
    totalCustomers: 50000,
    averageRating: 4.9,
    totalOrders: 25000,
    totalRevenue: 5000000000,
    deliveryTime: "2-3 ngày",
    satisfactionRate: 98.5,
    returnRate: 1.2,
    responseTime: "< 2 giờ",
    uptime: 99.9,
  };
}

export function useShopPerformanceStats() {
  const { stats, loading, error } = useShopStats();

  const performanceStats = stats ? {
    satisfactionRate: stats.satisfactionRate,
    returnRate: stats.returnRate,
    responseTime: stats.responseTime,
    uptime: stats.uptime,
    totalOrders: stats.totalOrders,
    totalRevenue: stats.totalRevenue,
  } : null;

  return {
    stats: performanceStats,
    loading,
    error,
  };
}

export function useShopGrowthStats() {
  const [growthStats, setGrowthStats] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchGrowthStats = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch("/api/shop/stats/growth");
      const data = await response.json();

      if (response.ok) {
        setGrowthStats(data.stats);
      } else {
        throw new Error(data.error || "Có lỗi xảy ra khi tải thống kê tăng trưởng");
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Có lỗi xảy ra khi tải thống kê tăng trưởng";
      setError(errorMessage);
      console.error("Fetch growth stats error:", error);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchGrowthStats();
  }, [fetchGrowthStats]);

  return {
    stats: growthStats,
    loading,
    error,
    refetch: fetchGrowthStats,
  };
}
