import { useState, useEffect, useCallback } from "react";
import { toast } from "sonner";

export interface Testimonial {
  id: string;
  name: string;
  role: string;
  avatar?: string;
  rating: number;
  content: string;
  product?: string;
  date: string;
  isVerified?: boolean;
  isFeatured?: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface TestimonialsFilters {
  featured?: boolean;
  rating?: number;
  product?: string;
  verified?: boolean;
}

export interface UseTestimonialsOptions {
  initialFilters?: TestimonialsFilters;
  limit?: number;
  autoRotate?: boolean;
  rotateInterval?: number;
}

export function useTestimonials(options: UseTestimonialsOptions = {}) {
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [filters, setFilters] = useState<TestimonialsFilters>(options.initialFilters || {});

  const fetchTestimonials = useCallback(async (customFilters?: TestimonialsFilters) => {
    setLoading(true);
    setError(null);
    
    try {
      const finalFilters = { ...filters, ...customFilters };
      
      // Since there's no public testimonials API yet, we'll use mock data for now
      // In the future, this would be: const response = await fetch(`/api/testimonials?${params}`);
      
      // Mock data for now - this should be replaced with actual API call when testimonials API is available
      const mockTestimonials: Testimonial[] = [
        {
          id: "1",
          name: "Nguyễn Thị Lan",
          role: "Khách hàng thân thiết",
          avatar: "/images/avatars/avatar-1.jpg",
          rating: 5,
          content: "Chất lượng sản phẩm tuyệt vời, giao hàng nhanh chóng. Tôi đã mua rất nhiều lần và luôn hài lòng với dịch vụ của NS Shop.",
          product: "Váy maxi hoa nhí",
          date: "2 tuần trước",
          isVerified: true,
          isFeatured: true,
          createdAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: "2",
          name: "Trần Văn Minh",
          role: "Blogger thời trang",
          avatar: "/images/avatars/avatar-2.jpg",
          rating: 5,
          content: "NS Shop có bộ sưu tập đa dạng và trendy. Đặc biệt là chất lượng vải rất tốt, giá cả hợp lý. Highly recommended!",
          product: "Áo thun cotton premium",
          date: "1 tuần trước",
          isVerified: true,
          isFeatured: true,
          createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: "3",
          name: "Lê Thị Hương",
          role: "Nhân viên văn phòng",
          avatar: "/images/avatars/avatar-3.jpg",
          rating: 5,
          content: "Mình rất thích phong cách phục vụ của NS Shop. Nhân viên tư vấn nhiệt tình, sản phẩm đúng như mô tả.",
          product: "Quần jeans skinny",
          date: "3 ngày trước",
          isVerified: true,
          isFeatured: false,
          createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: "4",
          name: "Phạm Đức Anh",
          role: "Sinh viên",
          avatar: "/images/avatars/avatar-4.jpg",
          rating: 4,
          content: "Giá cả phải chăng, phù hợp với sinh viên như mình. Chất lượng ổn, giao hàng đúng hẹn. Sẽ tiếp tục ủng hộ!",
          product: "Áo khoác blazer",
          date: "5 ngày trước",
          isVerified: true,
          isFeatured: false,
          createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: "5",
          name: "Võ Thị Mai",
          role: "Giáo viên",
          avatar: "/images/avatars/avatar-5.jpg",
          rating: 5,
          content: "Tôi đã giới thiệu NS Shop cho nhiều đồng nghiệp. Sản phẩm chất lượng, phù hợp với nhiều độ tuổi và phong cách.",
          product: "Túi xách da thật",
          date: "1 tuần trước",
          isVerified: true,
          isFeatured: true,
          createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: "6",
          name: "Hoàng Văn Đức",
          role: "Doanh nhân",
          avatar: "/images/avatars/avatar-6.jpg",
          rating: 5,
          content: "Dịch vụ chuyên nghiệp, sản phẩm chất lượng cao. Đặc biệt ấn tượng với bộ sưu tập suit công sở.",
          product: "Suit công sở",
          date: "4 ngày trước",
          isVerified: true,
          isFeatured: true,
          createdAt: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ];

      // Apply client-side filtering to mock data
      let filteredTestimonials = mockTestimonials;
      
      if (finalFilters.featured !== undefined) {
        filteredTestimonials = filteredTestimonials.filter(t => t.isFeatured === finalFilters.featured);
      }
      
      if (finalFilters.rating) {
        filteredTestimonials = filteredTestimonials.filter(t => t.rating >= finalFilters.rating!);
      }
      
      if (finalFilters.product) {
        filteredTestimonials = filteredTestimonials.filter(t => 
          t.product?.toLowerCase().includes(finalFilters.product!.toLowerCase())
        );
      }
      
      if (finalFilters.verified !== undefined) {
        filteredTestimonials = filteredTestimonials.filter(t => t.isVerified === finalFilters.verified);
      }

      // Apply limit
      if (options.limit) {
        filteredTestimonials = filteredTestimonials.slice(0, options.limit);
      }

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));
      
      setTestimonials(filteredTestimonials);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Có lỗi xảy ra khi tải đánh giá khách hàng";
      setError(errorMessage);
      console.error("Fetch testimonials error:", error);
    } finally {
      setLoading(false);
    }
  }, [filters, options.limit]);

  const nextTestimonial = useCallback(() => {
    setCurrentIndex(prev => (prev + 1) % testimonials.length);
  }, [testimonials.length]);

  const prevTestimonial = useCallback(() => {
    setCurrentIndex(prev => (prev - 1 + testimonials.length) % testimonials.length);
  }, [testimonials.length]);

  const goToTestimonial = useCallback((index: number) => {
    if (index >= 0 && index < testimonials.length) {
      setCurrentIndex(index);
    }
  }, [testimonials.length]);

  const updateFilters = useCallback((newFilters: Partial<TestimonialsFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  const clearFilters = useCallback(() => {
    setFilters({});
  }, []);

  // Auto-rotate testimonials
  useEffect(() => {
    if (options.autoRotate && testimonials.length > 1) {
      const interval = setInterval(nextTestimonial, options.rotateInterval || 5000);
      return () => clearInterval(interval);
    }
  }, [options.autoRotate, options.rotateInterval, nextTestimonial, testimonials.length]);

  useEffect(() => {
    fetchTestimonials();
  }, [fetchTestimonials]);

  return {
    testimonials,
    loading,
    error,
    currentIndex,
    currentTestimonial: testimonials[currentIndex],
    filters,
    fetchTestimonials,
    updateFilters,
    clearFilters,
    nextTestimonial,
    prevTestimonial,
    goToTestimonial,
  };
}

export function useFeaturedTestimonials(limit: number = 5) {
  const { testimonials, loading, error } = useTestimonials({ 
    initialFilters: { featured: true },
    limit 
  });

  return {
    testimonials,
    loading,
    error,
  };
}

export function useTestimonialRotation(testimonials: Testimonial[], autoRotate: boolean = true, interval: number = 5000) {
  const [currentIndex, setCurrentIndex] = useState(0);

  const nextTestimonial = useCallback(() => {
    setCurrentIndex(prev => (prev + 1) % testimonials.length);
  }, [testimonials.length]);

  const prevTestimonial = useCallback(() => {
    setCurrentIndex(prev => (prev - 1 + testimonials.length) % testimonials.length);
  }, [testimonials.length]);

  const goToTestimonial = useCallback((index: number) => {
    if (index >= 0 && index < testimonials.length) {
      setCurrentIndex(index);
    }
  }, [testimonials.length]);

  useEffect(() => {
    if (autoRotate && testimonials.length > 1) {
      const rotateInterval = setInterval(nextTestimonial, interval);
      return () => clearInterval(rotateInterval);
    }
  }, [autoRotate, interval, nextTestimonial, testimonials.length]);

  return {
    currentIndex,
    currentTestimonial: testimonials[currentIndex],
    nextTestimonial,
    prevTestimonial,
    goToTestimonial,
  };
}