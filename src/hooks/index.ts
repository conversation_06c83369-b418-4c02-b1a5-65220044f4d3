// Export attribute hooks
export { useAttributes } from "./useAttributes";
export { useAttributeValues } from "./useAttributeValues";

// Export product hooks
export {
  useProducts,
  useProduct,
  useProductBySlug,
  useFeaturedProducts,
  useTrendingProducts,
} from "./use-products";

// Export category hooks
export {
  useCategories,
  useCategory,
  useCategoryBySlug,
  useRootCategories,
  useCategoryTree,
  useCategoriesWithCounts,
  useFeaturedCategories,
} from "./use-categories";

// Export cart hooks
export {
  useCart,
  useCartItemCount,
  useQuickAddToCart,
  useCartValidation,
  useGuestCartMerge,
} from "./use-cart";

// Export user hooks
export {
  useUser,
  useUserAddresses,
  useUserWishlist,
  useUserPreferences,
} from "./use-user";

// Export order hooks
export {
  useOrders,
  useOrder,
  useOrderByTracking,
  useCreateOrder,
  useOrderStats,
  useOrderTimeline,
  useOrderStatus,
  useCancelOrder,
  useReturnOrder,
  useRecentOrders,
} from "./use-orders";

// Export search hooks
export {
  useSearch,
  useSearchSuggestions,
  useSearchHistory,
  usePopularSearches,
  useAdvancedSearch,
} from "./use-search";

// Export brands hooks
export {
  useBrands,
  useFeaturedBrands,
  useBrandsByCategory,
} from "./use-brands";

// Export testimonials hooks
export {
  useTestimonials,
  useFeaturedTestimonials,
  useTestimonialRotation,
} from "./use-testimonials";

// Export reviews hooks
export {
  useReviews,
  useProductReviews,
} from "./use-reviews";
