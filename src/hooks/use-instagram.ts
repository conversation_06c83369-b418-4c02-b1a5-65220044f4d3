import { useState, useEffect, useCallback } from "react";
import { toast } from "sonner";

export interface InstagramPost {
  id: string;
  image: string;
  caption?: string;
  likes: number;
  comments: number;
  permalink: string;
  timestamp: string;
  mediaType: "IMAGE" | "VIDEO" | "CAROUSEL_ALBUM";
  thumbnailUrl?: string;
}

export interface InstagramProfile {
  id: string;
  username: string;
  accountType: string;
  mediaCount: number;
  followersCount: number;
  followsCount: number;
}

interface UseInstagramOptions {
  limit?: number;
  autoFetch?: boolean;
}

export function useInstagramFeed(options: UseInstagramOptions = {}) {
  const { limit = 12, autoFetch = true } = options;
  
  const [posts, setPosts] = useState<InstagramPost[]>([]);
  const [profile, setProfile] = useState<InstagramProfile | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchInstagramFeed = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`/api/instagram/feed?limit=${limit}`);
      const data = await response.json();

      if (response.ok) {
        setPosts(data.posts || []);
        setProfile(data.profile || null);
      } else {
        throw new Error(data.error || "Có lỗi xảy ra khi tải Instagram feed");
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Có lỗi xảy ra khi tải Instagram feed";
      setError(errorMessage);
      console.error("Fetch Instagram feed error:", error);
      
      // Fallback to mock data if API fails
      setPosts(getMockInstagramPosts(limit));
    } finally {
      setLoading(false);
    }
  }, [limit]);

  useEffect(() => {
    if (autoFetch) {
      fetchInstagramFeed();
    }
  }, [autoFetch, fetchInstagramFeed]);

  const refetch = useCallback(() => {
    fetchInstagramFeed();
  }, [fetchInstagramFeed]);

  return {
    posts,
    profile,
    loading,
    error,
    refetch,
  };
}

export function useInstagramProfile() {
  const [profile, setProfile] = useState<InstagramProfile | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchProfile = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch("/api/instagram/profile");
      const data = await response.json();

      if (response.ok) {
        setProfile(data.profile);
      } else {
        throw new Error(data.error || "Có lỗi xảy ra khi tải thông tin Instagram");
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Có lỗi xảy ra khi tải thông tin Instagram";
      setError(errorMessage);
      console.error("Fetch Instagram profile error:", error);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchProfile();
  }, [fetchProfile]);

  return {
    profile,
    loading,
    error,
    refetch: fetchProfile,
  };
}

// Mock data fallback
function getMockInstagramPosts(limit: number = 12): InstagramPost[] {
  const mockPosts: InstagramPost[] = [
    {
      id: "1",
      image: "/images/instagram/post-1.jpg",
      caption: "Bộ sưu tập mới nhất từ NS Shop 💖 #fashion #style #nsshop",
      likes: 1234,
      comments: 56,
      permalink: "https://instagram.com/p/example1",
      timestamp: new Date().toISOString(),
      mediaType: "IMAGE",
    },
    {
      id: "2", 
      image: "/images/instagram/post-2.jpg",
      caption: "Xu hướng thời trang Thu Đông 2024 🍂 #autumn #fashion",
      likes: 987,
      comments: 43,
      permalink: "https://instagram.com/p/example2",
      timestamp: new Date().toISOString(),
      mediaType: "IMAGE",
    },
    {
      id: "3",
      image: "/images/instagram/post-3.jpg", 
      caption: "Phong cách công sở thanh lịch 👔 #office #professional",
      likes: 756,
      comments: 32,
      permalink: "https://instagram.com/p/example3",
      timestamp: new Date().toISOString(),
      mediaType: "IMAGE",
    },
    {
      id: "4",
      image: "/images/instagram/post-4.jpg",
      caption: "Trang phục dạo phố cuối tuần 🌟 #weekend #casual",
      likes: 654,
      comments: 28,
      permalink: "https://instagram.com/p/example4", 
      timestamp: new Date().toISOString(),
      mediaType: "IMAGE",
    },
    {
      id: "5",
      image: "/images/instagram/post-5.jpg",
      caption: "Bộ đồ dự tiệc sang trọng ✨ #party #elegant",
      likes: 543,
      comments: 21,
      permalink: "https://instagram.com/p/example5",
      timestamp: new Date().toISOString(),
      mediaType: "IMAGE",
    },
    {
      id: "6",
      image: "/images/instagram/post-6.jpg",
      caption: "Phụ kiện thời trang hot trend 💎 #accessories #jewelry",
      likes: 432,
      comments: 18,
      permalink: "https://instagram.com/p/example6",
      timestamp: new Date().toISOString(),
      mediaType: "IMAGE",
    },
    {
      id: "7",
      image: "/images/instagram/post-7.jpg",
      caption: "Giày dép thời trang mới nhất 👠 #shoes #footwear",
      likes: 321,
      comments: 15,
      permalink: "https://instagram.com/p/example7",
      timestamp: new Date().toISOString(),
      mediaType: "IMAGE",
    },
    {
      id: "8",
      image: "/images/instagram/post-8.jpg",
      caption: "Túi xách designer cao cấp 👜 #bags #luxury",
      likes: 210,
      comments: 12,
      permalink: "https://instagram.com/p/example8",
      timestamp: new Date().toISOString(),
      mediaType: "IMAGE",
    },
    {
      id: "9",
      image: "/images/instagram/post-9.jpg",
      caption: "Thời trang nam lịch lãm 🤵 #mensfashion #gentleman",
      likes: 198,
      comments: 9,
      permalink: "https://instagram.com/p/example9",
      timestamp: new Date().toISOString(),
      mediaType: "IMAGE",
    },
    {
      id: "10",
      image: "/images/instagram/post-10.jpg",
      caption: "Đồ thể thao năng động 🏃‍♀️ #sportswear #active",
      likes: 187,
      comments: 7,
      permalink: "https://instagram.com/p/example10",
      timestamp: new Date().toISOString(),
      mediaType: "IMAGE",
    },
    {
      id: "11",
      image: "/images/instagram/post-11.jpg",
      caption: "Trang phục mùa hè tươi mát 🌞 #summer #fresh",
      likes: 176,
      comments: 5,
      permalink: "https://instagram.com/p/example11",
      timestamp: new Date().toISOString(),
      mediaType: "IMAGE",
    },
    {
      id: "12",
      image: "/images/instagram/post-12.jpg",
      caption: "Behind the scenes tại NS Shop 📸 #behindthescenes #team",
      likes: 165,
      comments: 3,
      permalink: "https://instagram.com/p/example12",
      timestamp: new Date().toISOString(),
      mediaType: "IMAGE",
    },
  ];

  return mockPosts.slice(0, limit);
}
