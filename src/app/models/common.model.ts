/**
 * Common Models and Types
 * Các types và interfaces chung được sử dụng trong toàn bộ ứng dụng
 */

/**
 * Base Entity Interface
 * Tất cả entities đều có các fields cơ bản này
 */
export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Pagination Options
 */
export interface PaginationOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

/**
 * Paginated Result
 */
export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * Search Options
 */
export interface SearchOptions extends PaginationOptions {
  search?: string;
  filters?: Record<string, any>;
}

/**
 * Search Filters
 */
export interface SearchFilters extends PaginationOptions {
  search?: string;
  category?: string;
  brand?: string;
  minPrice?: number;
  maxPrice?: number;
  status?: ProductStatus;
  featured?: boolean;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
  filters?: Record<string, any>;
}

/**
 * API Response Wrapper
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  errors?: Record<string, string[]>;
}

/**
 * Status Enums
 */
export enum Status {
  ACTIVE = "ACTIVE",
  INACTIVE = "INACTIVE",
  DRAFT = "DRAFT",
  ARCHIVED = "ARCHIVED",
}

export enum OrderStatus {
  PENDING = "PENDING",
  CONFIRMED = "CONFIRMED",
  PROCESSING = "PROCESSING",
  SHIPPED = "SHIPPED",
  DELIVERED = "DELIVERED",
  CANCELLED = "CANCELLED",
  REFUNDED = "REFUNDED",
}

export enum PaymentStatus {
  PENDING = "PENDING",
  PAID = "PAID",
  FAILED = "FAILED",
  REFUNDED = "REFUNDED",
  CANCELLED = "CANCELLED",
}

export enum UserRole {
  USER = "USER",
  ADMIN = "ADMIN",
  SUPER_ADMIN = "SUPER_ADMIN",
}

export enum ProductStatus {
  ACTIVE = "ACTIVE",
  INACTIVE = "INACTIVE",
  OUT_OF_STOCK = "OUT_OF_STOCK",
}

export enum CommonReviewStatus {
  PENDING = "PENDING",
  APPROVED = "APPROVED",
  REJECTED = "REJECTED",
}

export enum PostStatus {
  DRAFT = "DRAFT",
  PUBLISHED = "PUBLISHED",
  ARCHIVED = "ARCHIVED",
}

/**
 * File Upload Types
 */
export interface FileUpload {
  filename: string;
  mimetype: string;
  size: number;
  url: string;
}

/**
 * Address Types
 */
export interface AddressData {
  street: string;
  city: string;
  state: string;
  country: string;
  postalCode: string;
  isDefault?: boolean;
}

/**
 * Contact Information
 */
export interface ContactInfo {
  email?: string;
  phone?: string;
  website?: string;
  socialMedia?: Record<string, string>;
}

/**
 * SEO Data
 */
export interface SEOData {
  title?: string;
  description?: string;
  keywords?: string[];
  ogImage?: string;
  canonicalUrl?: string;
}

/**
 * Audit Trail
 */
export interface AuditTrail {
  action: string;
  userId?: string;
  userEmail?: string;
  ipAddress?: string;
  userAgent?: string;
  metadata?: Record<string, any>;
  timestamp: Date;
}

/**
 * Error Types
 */
export class BusinessError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 400
  ) {
    super(message);
    this.name = "BusinessError";
  }
}

export class ValidationError extends BusinessError {
  constructor(
    message: string,
    public details?: Record<string, string[]>
  ) {
    super(message, "VALIDATION_ERROR", 400);
  }
}

export class NotFoundError extends BusinessError {
  constructor(resource: string, id?: string) {
    super(
      `${resource}${id ? ` with id ${id}` : ""} not found`,
      "NOT_FOUND",
      404
    );
  }
}

export class UnauthorizedError extends BusinessError {
  constructor(message: string = "Unauthorized") {
    super(message, "UNAUTHORIZED", 401);
  }
}

export class ForbiddenError extends BusinessError {
  constructor(message: string = "Forbidden") {
    super(message, "FORBIDDEN", 403);
  }
}

export class ConflictError extends BusinessError {
  constructor(message: string) {
    super(message, "CONFLICT", 409);
  }
}

/**
 * Event Types
 */
export interface DomainEvent {
  id: string;
  type: string;
  aggregateId: string;
  aggregateType: string;
  data: Record<string, any>;
  metadata?: Record<string, any>;
  timestamp: Date;
  version: number;
}

/**
 * Cache Types
 */
export interface CacheOptions {
  ttl?: number; // Time to live in seconds
  tags?: string[];
}

/**
 * Notification Types
 */
export enum CommonNotificationType {
  EMAIL = "EMAIL",
  SMS = "SMS",
  PUSH = "PUSH",
  IN_APP = "IN_APP",
}

export interface NotificationData {
  type: CommonNotificationType;
  recipient: string;
  subject?: string;
  content: string;
  metadata?: Record<string, any>;
}

/**
 * Analytics Types
 */
export interface AnalyticsEvent {
  event: string;
  userId?: string;
  sessionId?: string;
  properties?: Record<string, any>;
  timestamp: Date;
}

/**
 * Feature Flag Types
 */
export interface FeatureFlag {
  key: string;
  enabled: boolean;
  conditions?: Record<string, any>;
  rolloutPercentage?: number;
}

/**
 * Configuration Types
 */
export interface AppConfig {
  app: {
    name: string;
    version: string;
    environment: string;
    url: string;
  };
  database: {
    url: string;
  };
  redis?: {
    url: string;
  };
  email?: {
    provider: string;
    apiKey: string;
    from: string;
  };
  storage?: {
    provider: string;
    bucket: string;
    region: string;
  };
  payment?: {
    provider: string;
    publicKey: string;
    secretKey: string;
  };
}
