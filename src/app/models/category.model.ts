/**
 * Category Model
 * Business entity cho Category
 */

import { BaseEntity, SEOData } from "./common.model";
import { Status } from "@prisma/client";

/**
 * Category Entity
 */
export interface CategoryEntity extends BaseEntity {
  name: string;
  slug: string;
  description?: string | null;
  image?: string;
  imageId?: string | null;
  parentId?: string | null;
  status: Status;
  sortOrder: number;
  seo?: SEOData;
  metadata?: Record<string, any>;
  mediaId?: string | null;
}

/**
 * Category with Relations
 */
export interface CategoryWithRelations extends CategoryEntity {
  parent?: CategoryEntity;
  children?: CategoryEntity[];
  products?: any[]; // ProductEntity[]
  stats?: CategoryStats;
}

/**
 * Category Statistics
 */
export interface CategoryStats {
  totalProducts: number;
  activeProducts: number;
  totalSales: number;
  totalRevenue: number;
}

/**
 * Category Tree Node
 */
export interface CategoryTreeNode extends CategoryEntity {
  children: CategoryTreeNode[];
  level: number;
  path: string[];
}

/**
 * Category Creation Data
 */
export interface CreateCategoryData {
  name: string;
  slug?: string;
  description?: string;
  image?: string;
  parentId?: string;
  status?: Status;
  sortOrder?: number;
  seo?: SEOData;
  metadata?: Record<string, any>;
}

/**
 * Category Update Data
 */
export interface UpdateCategoryData {
  name?: string;
  slug?: string;
  description?: string;
  image?: string;
  parentId?: string;
  status?: Status;
  sortOrder?: number;
  seo?: SEOData;
  metadata?: Record<string, any>;
}

/**
 * Category Business Rules
 */
export class CategoryBusinessRules {
  static generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, "")
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-")
      .trim();
  }

  static validateSlug(slug: string): boolean {
    const slugRegex = /^[a-z0-9-]+$/;
    return slugRegex.test(slug) && slug.length >= 2 && slug.length <= 100;
  }

  static canHaveParent(category: CategoryEntity, parentId: string): boolean {
    // Cannot set self as parent
    if (category.id === parentId) return false;

    // Add logic to prevent circular references
    return true;
  }

  static buildCategoryTree(categories: CategoryEntity[]): CategoryTreeNode[] {
    const categoryMap = new Map<string, CategoryTreeNode>();
    const rootCategories: CategoryTreeNode[] = [];

    // Initialize all categories as tree nodes
    categories.forEach((category) => {
      categoryMap.set(category.id, {
        ...category,
        children: [],
        level: 0,
        path: [],
      });
    });

    // Build the tree structure
    categories.forEach((category) => {
      const node = categoryMap.get(category.id)!;

      if (category.parentId) {
        const parent = categoryMap.get(category.parentId);
        if (parent) {
          parent.children.push(node);
          node.level = parent.level + 1;
          node.path = [...parent.path, parent.id];
        }
      } else {
        rootCategories.push(node);
      }
    });

    return rootCategories;
  }

  static getCategoryPath(
    category: CategoryEntity,
    allCategories: CategoryEntity[]
  ): CategoryEntity[] {
    const path: CategoryEntity[] = [category];
    let current = category;

    while (current.parentId) {
      const parent = allCategories.find((c) => c.id === current.parentId);
      if (!parent) break;

      path.unshift(parent);
      current = parent;
    }

    return path;
  }

  static getDescendants(
    categoryId: string,
    allCategories: CategoryEntity[]
  ): CategoryEntity[] {
    const descendants: CategoryEntity[] = [];
    const children = allCategories.filter((c) => c.parentId === categoryId);

    children.forEach((child) => {
      descendants.push(child);
      descendants.push(...this.getDescendants(child.id, allCategories));
    });

    return descendants;
  }

  static wouldCreateCircularReference(
    categoryId: string,
    newParentId: string,
    allCategories: CategoryEntity[]
  ): boolean {
    // Check if the new parent is a descendant of the category
    const descendants = this.getDescendants(categoryId, allCategories);
    return descendants.some((desc) => desc.id === newParentId);
  }
}
