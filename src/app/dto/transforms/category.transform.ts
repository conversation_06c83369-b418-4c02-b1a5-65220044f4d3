/**
 * Category Transform Functions
 * Chuyển đổi giữa Category entities và DTOs
 */

import { CategoryEntity } from "../../models/category.model";
import { Status } from "@prisma/client";
import { StatusDto } from "../common.dto";
import { toISOString, removeUndefined } from "./common.transform";

import { CategoryResponseDto } from "../category.dto";

/**
 * Transform CategoryEntity to CategoryResponseDto
 */
export function categoryToResponseDto(
  category: CategoryEntity
): CategoryResponseDto {
  return removeUndefined({
    id: category.id,
    name: category.name,
    slug: category.slug,
    description: category.description,
    parentId: category.parentId,
    status: transformStatus(category.status),
    sortOrder: category.sortOrder,
    createdAt: toISOString(category.createdAt)!,
    updatedAt: toISOString(category.updatedAt)!,
    seo: category.seo,
    metadata: category.metadata,
  }) as CategoryResponseDto;
}

/**
 * Transform Status to StatusDto
 */
export function transformStatus(status: Status): StatusDto {
  switch (status) {
    case Status.ACTIVE:
      return StatusDto.ACTIVE;
    case Status.INACTIVE:
      return StatusDto.INACTIVE;
    case Status.DRAFT:
      return StatusDto.DRAFT;
    case Status.ARCHIVED:
      return StatusDto.ARCHIVED;
    default:
      return StatusDto.ACTIVE;
  }
}

/**
 * Transform StatusDto to Status
 */
export function transformStatusFromDto(status: StatusDto): Status {
  switch (status) {
    case StatusDto.ACTIVE:
      return Status.ACTIVE;
    case StatusDto.INACTIVE:
      return Status.INACTIVE;
    case StatusDto.DRAFT:
      return Status.DRAFT;
    case StatusDto.ARCHIVED:
      return Status.ARCHIVED;
    default:
      return Status.ACTIVE;
  }
}

/**
 * Transform array of categories to DTOs
 */
export function categoriesToResponseDtos(
  categories: CategoryEntity[]
): CategoryResponseDto[] {
  return categories.map(categoryToResponseDto);
}
