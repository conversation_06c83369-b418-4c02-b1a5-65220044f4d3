/**
 * Category DTOs
 * Data Transfer Objects cho Category
 */

import { z } from "zod";
import { StatusDto } from "./common.dto";
import { SlugSchema } from "./common.dto";

/**
 * Category Response DTO
 */
export interface CategoryResponseDto {
  id: string;
  name: string;
  slug: string;
  description?: string;
  parentId?: string;
  status: StatusDto;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
  seo?: CategorySEODataDto;
  metadata?: Record<string, any>;
  _count?: {
    products: number;
    children?: number;
  };
  children?: CategoryResponseDto[];
}

/**
 * SEO Data DTO for Category
 */
export interface CategorySEODataDto {
  title?: string;
  description?: string;
  keywords?: string[];
  ogImage?: string;
  canonicalUrl?: string;
}

/**
 * Create Category Request DTO
 */
export interface CreateCategoryRequestDto {
  name: string;
  description?: string;
  parentId?: string;
  status?: StatusDto;
  sortOrder?: number;
  seo?: CategorySEODataDto;
  metadata?: Record<string, any>;
}

/**
 * Update Category Request DTO
 */
export interface UpdateCategoryRequestDto {
  name?: string;
  description?: string;
  parentId?: string;
  status?: StatusDto;
  sortOrder?: number;
  seo?: CategorySEODataDto;
  metadata?: Record<string, any>;
}

/**
 * Validation Schemas
 */
export const CreateCategoryRequestSchema = z.object({
  name: z
    .string()
    .min(1, "Category name is required")
    .max(255, "Name too long"),
  description: z.string().optional(),
  parentId: z.string().uuid("Invalid parent ID").optional(),
  status: z.nativeEnum(StatusDto).default(StatusDto.ACTIVE),
  sortOrder: z.number().int().min(0).default(0),
  seo: z
    .object({
      title: z.string().max(60).optional(),
      description: z.string().max(160).optional(),
      keywords: z.array(z.string()).optional(),
      ogImage: z.string().url().optional(),
      canonicalUrl: z.string().url().optional(),
    })
    .optional(),
  metadata: z.record(z.any()).optional(),
});

export const UpdateCategoryRequestSchema =
  CreateCategoryRequestSchema.partial();

/**
 * Validation functions
 */
export const validateCreateCategory = (data: any): CreateCategoryRequestDto => {
  return CreateCategoryRequestSchema.parse(data);
};

export const validateUpdateCategory = (data: any): UpdateCategoryRequestDto => {
  return UpdateCategoryRequestSchema.parse(data);
};

/**
 * Type aliases for backward compatibility
 */
export type CategoryDto = CategoryResponseDto;
export type CreateCategoryDto = CreateCategoryRequestDto;
export type UpdateCategoryDto = UpdateCategoryRequestDto;
