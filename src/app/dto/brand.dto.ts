/**
 * Brand DTOs
 * Data Transfer Objects cho Brand
 */

import { z } from "zod";
import { StatusDto } from "./common.dto";
import { UrlSchema } from "./common.dto";

/**
 * Brand Response DTO
 */
export interface BrandResponseDto {
  id: string;
  name: string;
  slug: string;
  description?: string;
  logo?: string;
  website?: string;
  status: StatusDto;
  createdAt: string;
  updatedAt: string;
  seo?: BrandSEODataDto;
  metadata?: Record<string, any>;
  _count?: {
    products: number;
  };
}

/**
 * SEO Data DTO for Brand
 */
export interface BrandSEODataDto {
  title?: string;
  description?: string;
  keywords?: string[];
  ogImage?: string;
  canonicalUrl?: string;
}

/**
 * Create Brand Request DTO
 */
export interface CreateBrandRequestDto {
  name: string;
  description?: string;
  logo?: string;
  website?: string;
  status?: StatusDto;
  seo?: BrandSEODataDto;
  metadata?: Record<string, any>;
}

/**
 * Update Brand Request DTO
 */
export interface UpdateBrandRequestDto {
  name?: string;
  description?: string;
  logo?: string;
  website?: string;
  status?: StatusDto;
  seo?: BrandSEODataDto;
  metadata?: Record<string, any>;
}

/**
 * Validation Schemas
 */
export const CreateBrandRequestSchema = z.object({
  name: z.string().min(1, "Brand name is required").max(255, "Name too long"),
  description: z.string().optional(),
  logo: z.string().url("Invalid logo URL").optional(),
  website: UrlSchema.optional(),
  status: z.nativeEnum(StatusDto).default(StatusDto.ACTIVE),
  seo: z
    .object({
      title: z.string().max(60).optional(),
      description: z.string().max(160).optional(),
      keywords: z.array(z.string()).optional(),
      ogImage: z.string().url().optional(),
      canonicalUrl: z.string().url().optional(),
    })
    .optional(),
  metadata: z.record(z.any()).optional(),
});

export const UpdateBrandRequestSchema = CreateBrandRequestSchema.partial();

/**
 * Validation functions
 */
export const validateCreateBrand = (data: any): CreateBrandRequestDto => {
  return CreateBrandRequestSchema.parse(data);
};

export const validateUpdateBrand = (data: any): UpdateBrandRequestDto => {
  return UpdateBrandRequestSchema.parse(data);
};

/**
 * Type aliases for backward compatibility
 */
export type BrandDto = BrandResponseDto;
export type CreateBrandDto = CreateBrandRequestDto;
export type UpdateBrandDto = UpdateBrandRequestDto;
