import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../auth/[...nextauth]/route";
import { z } from "zod";
import { container, SERVICE_IDENTIFIERS } from "../di-container";
import { CartService } from "../services/cart.service";
import { UserService } from "../services/user.service";
import { initializeSystem } from "../initialize";

const addToCartSchema = z.object({
  productId: z.string().min(1, "Product ID là bắt buộc"),
  quantity: z.number().int().min(1, "Số lượng phải lớn hơn 0"),
});

const _updateCartItemSchema = z.object({
  quantity: z.number().int().min(0, "Số lượng không được âm"),
});

// GET /api/cart - Lấy giỏ hàng của user
export async function GET(_request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user || !session.user.id) {
      return NextResponse.json(
        { error: "Vui lòng đăng nhập" },
        { status: 401 }
      );
    }

    // Initialize system and get services
    initializeSystem();
    const userService = container.resolve<UserService>(SERVICE_IDENTIFIERS.USER_SERVICE);
    const cartService = container.resolve<CartService>(SERVICE_IDENTIFIERS.CART_SERVICE);

    // Kiểm tra user có tồn tại trong database không
    const user = await userService.getUserById(session.user.id);
    if (!user) {
      console.error("User not found in database:", session.user.id);
      return NextResponse.json(
        { error: "Người dùng không tồn tại" },
        { status: 404 }
      );
    }

    // Lấy hoặc tạo giỏ hàng
    const cart = await cartService.getOrCreateCart(session.user.id);

    return NextResponse.json(cart);
  } catch (error) {
    console.error("Get cart error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy giỏ hàng" },
      { status: 500 }
    );
  }
}

// POST /api/cart - Thêm sản phẩm vào giỏ hàng
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user || !session.user.id) {
      return NextResponse.json(
        { error: "Vui lòng đăng nhập" },
        { status: 401 }
      );
    }

    // Initialize system and get services
    initializeSystem();
    const userService = container.resolve<UserService>(SERVICE_IDENTIFIERS.USER_SERVICE);
    const cartService = container.resolve<CartService>(SERVICE_IDENTIFIERS.CART_SERVICE);

    // Kiểm tra user có tồn tại trong database không
    const user = await userService.getUserById(session.user.id);
    if (!user) {
      console.error("User not found in database:", session.user.id);
      return NextResponse.json(
        { error: "Người dùng không tồn tại" },
        { status: 404 }
      );
    }

    const body = await request.json();
    const { productId, quantity } = addToCartSchema.parse(body);

    // Sử dụng service method để thêm vào giỏ hàng
    const cart = await cartService.addToCart(
      session.user.id,
      productId,
      quantity
    );

    return NextResponse.json({
      message: "Thêm vào giỏ hàng thành công",
      cart,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error("Add to cart error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi thêm vào giỏ hàng" },
      { status: 500 }
    );
  }
}
