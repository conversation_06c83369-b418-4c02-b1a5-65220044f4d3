/**
 * Media Service
 * Business logic cho Media management
 */

import { BaseService } from "./base.service";
import { Injectable, SERVICE_IDENTIFIERS } from "../di-container";
import { MediaRepository } from "../repositories";
import {
  MediaEntity,
  CreateMediaData,
  UpdateMediaData,
  MediaBusinessRules,
  MediaType,
} from "../../models/media.model";
import {
  PaginatedResult,
  SearchFilters,
  NotFoundError,
  ValidationError,
  ForbiddenError,
} from "../../models/common.model";
import { UserEntity } from "../../models/user.model";

// Service identifier
export const MEDIA_SERVICE = Symbol("MediaService");

export class MediaService extends BaseService {
  private mediaRepository: MediaRepository;

  constructor() {
    super();
    this.mediaRepository = this.getRepository<MediaRepository>(
      SERVICE_IDENTIFIERS.MEDIA_REPOSITORY
    );
  }

  /**
   * Upload media file
   */
  async uploadMedia(
    data: CreateMediaData,
    uploadedBy: UserEntity
  ): Promise<MediaEntity> {
    return this.executeWithErrorHandling(async () => {
      // Validate input
      this.validateRequired(data, [
        "filename",
        "originalName",
        "mimeType",
        "size",
        "url",
      ]);

      // Validate file
      const validation = MediaBusinessRules.validateFile(data);
      if (!validation.valid) {
        throw new ValidationError(validation.errors.join(", "));
      }

      // Check file size limit
      if (!MediaBusinessRules.isValidFileSize(data.size)) {
        throw new ValidationError("File size exceeds limit");
      }

      // Check file type
      if (!MediaBusinessRules.isAllowedFileType(data.mimeType)) {
        throw new ValidationError("File type not allowed");
      }

      // Generate alt text if not provided
      const altText =
        data.alt || MediaBusinessRules.generateAltText(data.originalName);

      // Create media record
      const mediaData = {
        filename: data.filename,
        path: data.url, // Use URL as path for now
        url: data.url,
        mimeType: data.mimeType,
        size: data.size,
        alt: altText,
        folder: data.folder || "uploads",
        type: "INTERNAL" as const,
      };

      const media = (await this.mediaRepository.create(
        mediaData
      )) as unknown as MediaEntity;

      // Log activity
      await this.logActivity("MEDIA_UPLOADED", uploadedBy.id, {
        mediaId: media.id,
        filename: data.filename,
        mimeType: data.mimeType,
        size: data.size,
      });

      return media;
    }, "uploadMedia");
  }

  /**
   * Lấy media theo ID
   */
  async getMediaById(id: string): Promise<MediaEntity> {
    return this.executeWithErrorHandling(async () => {
      const media = await this.mediaRepository.findById(id);
      if (!media) {
        throw new NotFoundError("Media", id);
      }
      return media as unknown as MediaEntity;
    }, "getMediaById");
  }

  /**
   * Cập nhật media metadata
   */
  async updateMedia(
    id: string,
    data: UpdateMediaData,
    updatedBy: UserEntity
  ): Promise<MediaEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check if media exists
      const existingMedia = await this.mediaRepository.findById(id);
      if (!existingMedia) {
        throw new NotFoundError("Media", id);
      }

      // Check admin permission for updates
      if (!this.isAdmin(updatedBy)) {
        throw new ForbiddenError("Only admins can update media");
      }

      // Update media
      const updatedMedia = (await this.mediaRepository.update(
        id,
        data
      )) as unknown as MediaEntity;

      // Log activity
      await this.logActivity("MEDIA_UPDATED", updatedBy.id, {
        mediaId: id,
        changes: data,
      });

      return updatedMedia;
    }, "updateMedia");
  }

  /**
   * Xóa media
   */
  async deleteMedia(id: string, deletedBy: UserEntity): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      // Check if media exists
      const media = await this.mediaRepository.findById(id);
      if (!media) {
        throw new NotFoundError("Media", id);
      }

      // Check admin permission for deletion
      if (!this.isAdmin(deletedBy)) {
        throw new ForbiddenError("Only admins can delete media");
      }

      // Check if media can be deleted (assuming no usage for now)
      const canDeleteResult = MediaBusinessRules.canDelete(
        media as unknown as MediaEntity,
        0
      );
      if (!canDeleteResult.canDelete) {
        throw new ValidationError(
          canDeleteResult.reason || "Cannot delete media"
        );
      }

      // TODO: Check if media is being used in products, posts, etc.
      // const isInUse = await this.checkMediaUsage(id);
      // if (isInUse) {
      //   throw new ValidationError('Cannot delete media that is being used');
      // }

      // Delete media file from storage
      // TODO: Implement file deletion from storage service
      // await this.deleteFileFromStorage(media.url);

      // Delete media record
      await this.mediaRepository.delete(id);

      // Log activity
      await this.logActivity("MEDIA_DELETED", deletedBy.id, {
        mediaId: id,
        filename: media.filename,
      });
    }, "deleteMedia");
  }

  /**
   * Lấy danh sách media với phân trang
   */
  async getMediaList(
    filters: SearchFilters & {
      type?: "image" | "video" | "document";
      uploadedBy?: string;
    } = {}
  ): Promise<PaginatedResult<MediaEntity>> {
    return this.executeWithErrorHandling(async () => {
      // Validate pagination
      const page = filters.page || 1;
      const limit = filters.limit || 20;
      this.validatePagination(page, limit);

      // Build search conditions
      const searchConditions: any = {};

      if (filters.search) {
        const searchQuery = this.sanitizeSearchQuery(filters.search);
        searchConditions.OR = [
          { originalName: { contains: searchQuery, mode: "insensitive" } },
          { altText: { contains: searchQuery, mode: "insensitive" } },
        ];
      }

      if (filters.type) {
        // Convert string to MediaType enum
        let mediaType: MediaType;
        switch (filters.type) {
          case "image":
            mediaType = MediaType.IMAGE;
            break;
          case "video":
            mediaType = MediaType.VIDEO;
            break;
          case "document":
            mediaType = MediaType.DOCUMENT;
            break;
          default:
            mediaType = MediaType.OTHER;
        }
        const mimeTypes = MediaBusinessRules.getMimeTypesByCategory(mediaType);
        searchConditions.mimeType = { in: mimeTypes };
      }

      if (filters.uploadedBy) {
        searchConditions.uploadedBy = filters.uploadedBy;
      }

      if (filters.status) {
        searchConditions.status = filters.status;
      }

      // Get media with pagination
      const result = await this.mediaRepository.findWithPagination({
        page,
        limit,
        where: searchConditions,
        orderBy: {
          [filters.sortBy || "createdAt"]: filters.sortOrder || "desc",
        },
      });

      return {
        data: result.data.map((media) => media as unknown as MediaEntity),
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
        hasNext: result.hasNext,
        hasPrev: result.hasPrev,
      };
    }, "getMediaList");
  }

  /**
   * Lấy images
   */
  async getImages(
    filters: SearchFilters = {}
  ): Promise<PaginatedResult<MediaEntity>> {
    return this.executeWithErrorHandling(async () => {
      return await this.getMediaList({ ...filters, type: "image" });
    }, "getImages");
  }

  /**
   * Lấy videos
   */
  async getVideos(
    filters: SearchFilters = {}
  ): Promise<PaginatedResult<MediaEntity>> {
    return this.executeWithErrorHandling(async () => {
      return await this.getMediaList({ ...filters, type: "video" });
    }, "getVideos");
  }

  /**
   * Lấy documents
   */
  async getDocuments(
    filters: SearchFilters = {}
  ): Promise<PaginatedResult<MediaEntity>> {
    return this.executeWithErrorHandling(async () => {
      return await this.getMediaList({ ...filters, type: "document" });
    }, "getDocuments");
  }

  /**
   * Lấy recent media
   */
  async getRecentMedia(limit: number = 20): Promise<MediaEntity[]> {
    return this.executeWithErrorHandling(async () => {
      const media = await this.mediaRepository.getRecentMedia(limit);
      return media.map((item) => item as unknown as MediaEntity);
    }, "getRecentMedia");
  }

  /**
   * Lấy media theo mime type
   */
  async getMediaByMimeType(mimeType: string): Promise<MediaEntity[]> {
    return this.executeWithErrorHandling(async () => {
      const media = await this.mediaRepository.getMediaByMimeType(mimeType);
      return media.map((item) => item as unknown as MediaEntity);
    }, "getMediaByMimeType");
  }

  /**
   * Bulk delete media
   */
  async bulkDeleteMedia(
    ids: string[],
    deletedBy: UserEntity
  ): Promise<{
    success: string[];
    failed: { id: string; reason: string }[];
  }> {
    return this.executeWithErrorHandling(async () => {
      const success: string[] = [];
      const failed: { id: string; reason: string }[] = [];

      for (const id of ids) {
        try {
          await this.deleteMedia(id, deletedBy);
          success.push(id);
        } catch (error) {
          failed.push({
            id,
            reason: error instanceof Error ? error.message : "Unknown error",
          });
        }
      }

      // Log activity
      await this.logActivity("MEDIA_BULK_DELETED", deletedBy.id, {
        successCount: success.length,
        failedCount: failed.length,
      });

      return { success, failed };
    }, "bulkDeleteMedia");
  }

  /**
   * Optimize media (resize, compress, etc.)
   */
  async optimizeMedia(
    id: string,
    options: {
      width?: number;
      height?: number;
      quality?: number;
    },
    requestedBy: UserEntity
  ): Promise<MediaEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check if media exists
      const media = await this.mediaRepository.findById(id);
      if (!media) {
        throw new NotFoundError("Media", id);
      }

      // Check admin permission for optimization
      if (!this.isAdmin(requestedBy)) {
        throw new ForbiddenError("Only admins can optimize media");
      }

      // Check if media is an image
      if (!media.mimeType.startsWith("image/")) {
        throw new ValidationError("Only images can be optimized");
      }

      // TODO: Implement image optimization
      // const optimizedUrl = await this.optimizeImage(media.url, options);

      // Update media with optimized version
      // For now, just return the media as optimized
      // TODO: Implement actual optimization logic
      const updatedMedia = media as unknown as MediaEntity;

      // Log activity
      await this.logActivity("MEDIA_OPTIMIZED", requestedBy.id, {
        mediaId: id,
        options,
      });

      return updatedMedia;
    }, "optimizeMedia");
  }

  /**
   * Generate thumbnails for media
   */
  async generateThumbnails(
    id: string,
    sizes: { width: number; height: number }[],
    requestedBy: UserEntity
  ): Promise<MediaEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check if media exists
      const media = await this.mediaRepository.findById(id);
      if (!media) {
        throw new NotFoundError("Media", id);
      }

      // Check admin permission for thumbnail generation
      if (!this.isAdmin(requestedBy)) {
        throw new ForbiddenError("Only admins can generate thumbnails");
      }

      // Check if media is an image or video
      if (
        !media.mimeType.startsWith("image/") &&
        !media.mimeType.startsWith("video/")
      ) {
        throw new ValidationError("Only images and videos can have thumbnails");
      }

      // TODO: Implement thumbnail generation
      // const thumbnails = await this.generateImageThumbnails(media.url, sizes);

      // For now, just return the media as is
      // TODO: Implement actual thumbnail generation logic
      const updatedMedia = media as unknown as MediaEntity;

      // Log activity
      await this.logActivity("MEDIA_THUMBNAILS_GENERATED", requestedBy.id, {
        mediaId: id,
        thumbnailSizes: sizes,
      });

      return updatedMedia;
    }, "generateThumbnails");
  }

  /**
   * Helper method to check if user is admin
   */
  private isAdmin(user: UserEntity): boolean {
    return ["ADMIN", "SUPER_ADMIN"].includes(user.role as string);
  }
}
