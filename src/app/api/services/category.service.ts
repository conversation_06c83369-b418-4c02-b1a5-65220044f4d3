/**
 * Category Service
 * Business logic cho Category management
 */

import { SERVICE_IDENTIFIERS } from "../di-container";
import { BaseService } from "./base.service";
import { CategoryRepository } from "../repositories";
import {
  CategoryEntity,
  CategoryWithRelations,
  CreateCategoryData,
  UpdateCategoryData,
  CategoryBusinessRules,
  CategoryTreeNode,
} from "../../models/category.model";
import { PaginatedResult, SearchFilters } from "../../models/common.model";
import { UserEntity } from "../../models/user.model";

// Service identifier
export const CATEGORY_SERVICE = Symbol("CategoryService");

export class CategoryService extends BaseService {
  private categoryRepository: CategoryRepository;

  constructor() {
    super();
    this.categoryRepository = this.getRepository<CategoryRepository>(
      SERVICE_IDENTIFIERS.CATEGORY_REPOSITORY
    );
  }

  /**
   * T<PERSON><PERSON> danh mục mới
   */
  async createCategory(
    data: CreateCategoryData,
    createdBy: UserEntity
  ): Promise<CategoryEntity> {
    try {
      // Validate permissions
      this.validatePermission(createdBy, "category:create");

      // Validate required fields
      this.validateRequired(data, ["name"]);

      // Generate slug if not provided
      const slug = data.slug || CategoryBusinessRules.generateSlug(data.name);

      // Check slug uniqueness
      const existingCategory = await this.categoryRepository.findBySlug(slug);
      if (existingCategory) {
        throw new Error("Category slug already exists");
      }

      // Validate parent category if provided
      if (data.parentId) {
        const parentCategory = await this.categoryRepository.findById(
          data.parentId
        );
        if (!parentCategory) {
          throw new Error("Parent category not found");
        }

        // Check for circular reference
        const allCategories = await this.categoryRepository.findMany();
        if (
          CategoryBusinessRules.wouldCreateCircularReference(
            data.parentId,
            slug,
            allCategories
          )
        ) {
          throw new Error(
            "Cannot create circular reference in category hierarchy"
          );
        }
      }

      // Create category
      const { image, ...restData } = data;
      const categoryData = {
        ...restData,
        slug,
        imageId: image, // Transform image string to imageId
        createdBy: createdBy.id,
      };

      const category = await this.categoryRepository.create(categoryData);

      // Log activity
      await this.logActivity("category:created", createdBy.id, {
        categoryId: category.id,
        categoryName: category.name,
      });

      return category;
    } catch (error) {
      this.handleError(error, "CategoryService.createCategory");
    }
  }

  /**
   * Cập nhật danh mục
   */
  async updateCategory(
    id: string,
    data: UpdateCategoryData,
    updatedBy: UserEntity
  ): Promise<CategoryEntity> {
    try {
      // Validate permissions
      this.validatePermission(updatedBy, "category:update");

      // Get existing category
      const existingCategory = await this.categoryRepository.findById(id);
      if (!existingCategory) {
        throw new Error("Category not found");
      }

      // Handle slug update
      if (data.name && data.name !== existingCategory.name) {
        const newSlug = CategoryBusinessRules.generateSlug(data.name);
        if (newSlug !== existingCategory.slug) {
          const existingSlug =
            await this.categoryRepository.findBySlug(newSlug);
          if (existingSlug && existingSlug.id !== id) {
            throw new Error("Category slug already exists");
          }
          data.slug = newSlug;
        }
      }

      // Validate parent category if changed
      if (data.parentId && data.parentId !== existingCategory.parentId) {
        const parentCategory = await this.categoryRepository.findById(
          data.parentId
        );
        if (!parentCategory) {
          throw new Error("Parent category not found");
        }

        // Check for circular reference
        const allCategories = await this.categoryRepository.findMany();
        if (
          CategoryBusinessRules.wouldCreateCircularReference(
            data.parentId,
            id,
            allCategories
          )
        ) {
          throw new Error(
            "Cannot create circular reference in category hierarchy"
          );
        }
      }

      // Update category
      const { image, ...restData } = data;
      const updateData = {
        ...restData,
        imageId: image, // Transform image string to imageId
        updatedBy: updatedBy.id,
        updatedAt: new Date(),
      };

      const category = await this.categoryRepository.update(id, updateData);

      // Log activity
      await this.logActivity("category:updated", updatedBy.id, {
        categoryId: category.id,
        categoryName: category.name,
        changes: Object.keys(data),
      });

      return category;
    } catch (error) {
      this.handleError(error, "CategoryService.updateCategory");
    }
  }

  /**
   * Xóa danh mục
   */
  async deleteCategory(id: string, deletedBy: UserEntity): Promise<void> {
    try {
      // Validate permissions
      this.validatePermission(deletedBy, "category:delete");

      // Get existing category
      const existingCategory = await this.categoryRepository.findById(id);
      if (!existingCategory) {
        throw new Error("Category not found");
      }

      // Check if category has children
      const children = await this.categoryRepository.findChildren(id);
      if (children.length > 0) {
        throw new Error("Cannot delete category with subcategories");
      }

      // Check if category has products
      const productCount = await this.categoryRepository.getProductCount(id);
      if (productCount > 0) {
        throw new Error("Cannot delete category with products");
      }

      // Soft delete
      await this.categoryRepository.softDelete(id, deletedBy.id);

      // Log activity
      await this.logActivity("category:deleted", deletedBy.id, {
        categoryId: id,
        categoryName: existingCategory.name,
      });
    } catch (error) {
      this.handleError(error, "CategoryService.deleteCategory");
    }
  }

  /**
   * Lấy danh mục theo ID
   */
  async getCategoryById(
    id: string,
    includeRelations = false
  ): Promise<CategoryEntity | CategoryWithRelations | null> {
    try {
      if (includeRelations) {
        return (await this.categoryRepository.findByIdWithRelations(
          id
        )) as CategoryWithRelations | null;
      }
      return (await this.categoryRepository.findById(
        id
      )) as CategoryEntity | null;
    } catch (error) {
      this.handleError(error, "CategoryService.getCategoryById");
    }
  }

  /**
   * Lấy danh mục theo slug
   */
  async getCategoryBySlug(
    slug: string,
    includeRelations = false
  ): Promise<CategoryEntity | CategoryWithRelations | null> {
    try {
      if (includeRelations) {
        return (await this.categoryRepository.findBySlugWithRelations(
          slug
        )) as CategoryWithRelations | null;
      }
      return (await this.categoryRepository.findBySlug(
        slug
      )) as CategoryEntity | null;
    } catch (error) {
      this.handleError(error, "CategoryService.getCategoryBySlug");
    }
  }

  /**
   * Lấy tất cả danh mục
   */
  async getAllCategories(): Promise<CategoryEntity[]> {
    try {
      return (await this.categoryRepository.getAllCategoriesWithHierarchy()) as CategoryEntity[];
    } catch (error) {
      this.handleError(error, "CategoryService.getAllCategories");
    }
  }

  /**
   * Lấy danh mục root (không có parent)
   */
  async getRootCategories(): Promise<CategoryEntity[]> {
    try {
      return (await this.categoryRepository.findMany({
        where: { parentId: null },
      })) as CategoryEntity[];
    } catch (error) {
      this.handleError(error, "CategoryService.getRootCategories");
    }
  }

  /**
   * Lấy danh mục con
   */
  async getChildCategories(parentId: string): Promise<CategoryEntity[]> {
    try {
      return await this.categoryRepository.findChildren(parentId);
    } catch (error) {
      this.handleError(error, "CategoryService.getChildCategories");
    }
  }

  /**
   * Xây dựng cây danh mục
   */
  async getCategoryTree(): Promise<CategoryTreeNode[]> {
    try {
      const categories = await this.categoryRepository.findMany();
      return CategoryBusinessRules.buildCategoryTree(
        categories as CategoryEntity[]
      );
    } catch (error) {
      this.handleError(error, "CategoryService.getCategoryTree");
    }
  }

  /**
   * Tìm kiếm danh mục
   */
  async searchCategories(
    filters: SearchFilters
  ): Promise<PaginatedResult<CategoryEntity>> {
    try {
      return await this.categoryRepository.search(filters);
    } catch (error) {
      this.handleError(error, "CategoryService.searchCategories");
    }
  }

  /**
   * Sắp xếp lại thứ tự danh mục
   */
  async reorderCategories(
    categoryOrders: { id: string; sortOrder: number }[],
    updatedBy: UserEntity
  ): Promise<void> {
    try {
      // Validate permissions
      this.validatePermission(updatedBy, "category:reorder");

      // Update sort orders
      await this.categoryRepository.updateSortOrders(categoryOrders);

      // Log activity
      await this.logActivity("category:reordered", updatedBy.id, {
        categoryCount: categoryOrders.length,
      });
    } catch (error) {
      this.handleError(error, "CategoryService.reorderCategories");
    }
  }
}
