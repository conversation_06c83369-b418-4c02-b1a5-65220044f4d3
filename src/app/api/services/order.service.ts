/**
 * Order Service
 * Business logic cho Order management
 */

import { SERVICE_IDENTIFIERS } from "../di-container";
import { BaseService } from "./base.service";
import { OrderRepository, UserRepository } from "../repositories";
import {
  OrderSearchOptions,
  OrderWithRelations,
} from "../repositories/order.repository";
import { Order, OrderStatus } from "@prisma/client";
import { PaginatedResult } from "../../models/common.model";

// Service identifier
export const ORDER_SERVICE = Symbol("OrderService");

export class OrderService extends BaseService {
  private orderRepository: OrderRepository;
  private userRepository: UserRepository;

  constructor() {
    super();
    this.orderRepository = this.getRepository<OrderRepository>(
      SERVICE_IDENTIFIERS.ORDER_REPOSITORY
    );
    this.userRepository = this.getRepository<UserRepository>(
      SERVICE_IDENTIFIERS.USER_REPOSITORY
    );
  }

  /**
   * Tìm kiếm đơn hàng với phân trang
   */
  async searchOrders(
    filters: OrderSearchOptions
  ): Promise<PaginatedResult<Order>> {
    try {
      return await this.orderRepository.searchOrders(filters);
    } catch (error) {
      this.handleError(error, "OrderService.searchOrders");
    }
  }

  /**
   * Lấy đơn hàng theo ID
   */
  async getOrderById(
    id: string,
    includeRelations = false
  ): Promise<Order | OrderWithRelations | null> {
    try {
      if (includeRelations) {
        return await this.orderRepository.findByIdWithRelations(id);
      }
      return await this.orderRepository.findById(id);
    } catch (error) {
      this.handleError(error, "OrderService.getOrderById");
    }
  }

  /**
   * Lấy đơn hàng của user
   */
  async findUserOrders(
    userId: string,
    options: {
      page?: number;
      limit?: number;
      status?: OrderStatus;
    } = {}
  ): Promise<PaginatedResult<Order>> {
    try {
      return await this.orderRepository.findUserOrders(userId, options);
    } catch (error) {
      this.handleError(error, "OrderService.findUserOrders");
    }
  }
}
