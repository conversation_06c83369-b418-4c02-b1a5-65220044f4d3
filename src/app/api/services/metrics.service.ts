/**
 * Metrics Service
 * Performance monitoring and metrics collection
 */

export interface Metric {
  name: string;
  value: number;
  timestamp: Date;
  tags?: Record<string, string>;
  type: "counter" | "gauge" | "histogram" | "timer";
}

export interface TimerResult {
  duration: number;
  end: () => number;
}

export interface MetricsSummary {
  totalRequests: number;
  averageResponseTime: number;
  errorRate: number;
  cacheHitRate: number;
  activeConnections: number;
  memoryUsage: NodeJS.MemoryUsage;
  uptime: number;
}

export interface PerformanceAlert {
  metric: string;
  threshold: number;
  currentValue: number;
  severity: "low" | "medium" | "high" | "critical";
  timestamp: Date;
}

export class MetricsService {
  private static instance: MetricsService;
  private metrics = new Map<string, Metric[]>();
  private timers = new Map<string, number>();
  private alerts: PerformanceAlert[] = [];
  private readonly maxMetricsPerType = 1000;
  private readonly alertThresholds = {
    responseTime: 1000, // ms
    errorRate: 0.05, // 5%
    memoryUsage: 0.8, // 80%
    cacheHitRate: 0.7, // 70%
  };

  private constructor() {
    this.startSystemMetricsCollection();
  }

  static getInstance(): MetricsService {
    if (!MetricsService.instance) {
      MetricsService.instance = new MetricsService();
    }
    return MetricsService.instance;
  }

  /**
   * Record a counter metric (incremental value)
   */
  counter(
    name: string,
    value: number = 1,
    tags?: Record<string, string>
  ): void {
    this.recordMetric({
      name,
      value,
      timestamp: new Date(),
      tags,
      type: "counter",
    });
  }

  /**
   * Record a gauge metric (current value)
   */
  gauge(name: string, value: number, tags?: Record<string, string>): void {
    this.recordMetric({
      name,
      value,
      timestamp: new Date(),
      tags,
      type: "gauge",
    });
  }

  /**
   * Record a histogram metric (distribution of values)
   */
  histogram(name: string, value: number, tags?: Record<string, string>): void {
    this.recordMetric({
      name,
      value,
      timestamp: new Date(),
      tags,
      type: "histogram",
    });
  }

  /**
   * Start a timer and return a function to end it
   */
  timer(name: string, tags?: Record<string, string>): TimerResult {
    const startTime = Date.now();
    const timerId = `${name}_${startTime}_${Math.random()}`;

    this.timers.set(timerId, startTime);

    const end = (): number => {
      const endTime = Date.now();
      const duration = endTime - startTime;

      this.recordMetric({
        name,
        value: duration,
        timestamp: new Date(),
        tags,
        type: "timer",
      });

      this.timers.delete(timerId);
      return duration;
    };

    return {
      duration: 0,
      end,
    };
  }

  /**
   * Time a function execution
   */
  async timeFunction<T>(
    name: string,
    fn: () => Promise<T> | T,
    tags?: Record<string, string>
  ): Promise<T> {
    const timer = this.timer(name, tags);
    try {
      const result = await fn();
      timer.end();
      return result;
    } catch (error) {
      timer.end();
      this.counter("function_errors", 1, { ...tags, function: name });
      throw error;
    }
  }

  /**
   * Record API request metrics
   */
  recordApiRequest(
    method: string,
    path: string,
    statusCode: number,
    responseTime: number,
    userId?: string
  ): void {
    const tags = {
      method,
      path,
      status: statusCode.toString(),
      ...(userId && { userId }),
    };

    this.counter("api_requests_total", 1, tags);
    this.histogram("api_response_time", responseTime, tags);

    if (statusCode >= 400) {
      this.counter("api_errors_total", 1, tags);
    }

    // Check for performance alerts
    this.checkResponseTimeAlert(responseTime);
  }

  /**
   * Record database query metrics
   */
  recordDatabaseQuery(
    operation: string,
    table: string,
    duration: number,
    success: boolean
  ): void {
    const tags = {
      operation,
      table,
      success: success.toString(),
    };

    this.counter("db_queries_total", 1, tags);
    this.histogram("db_query_duration", duration, tags);

    if (!success) {
      this.counter("db_errors_total", 1, tags);
    }
  }

  /**
   * Record cache metrics
   */
  recordCacheOperation(
    operation: "hit" | "miss" | "set" | "delete",
    key: string,
    duration?: number
  ): void {
    const tags = {
      operation,
      key_prefix: key.split(":")[0] || "unknown",
    };

    this.counter("cache_operations_total", 1, tags);

    if (duration !== undefined) {
      this.histogram("cache_operation_duration", duration, tags);
    }
  }

  /**
   * Get metrics summary
   */
  getSummary(): MetricsSummary {
    const apiRequests = this.getMetricValues("api_requests_total");
    const responseTimes = this.getMetricValues("api_response_time");
    const apiErrors = this.getMetricValues("api_errors_total");
    const cacheHits = this.getMetricValues("cache_operations_total", {
      operation: "hit",
    });
    const cacheMisses = this.getMetricValues("cache_operations_total", {
      operation: "miss",
    });

    const totalRequests = apiRequests.reduce((sum, m) => sum + m.value, 0);
    const totalErrors = apiErrors.reduce((sum, m) => sum + m.value, 0);
    const totalCacheHits = cacheHits.reduce((sum, m) => sum + m.value, 0);
    const totalCacheMisses = cacheMisses.reduce((sum, m) => sum + m.value, 0);

    const averageResponseTime =
      responseTimes.length > 0
        ? responseTimes.reduce((sum, m) => sum + m.value, 0) /
          responseTimes.length
        : 0;

    const errorRate = totalRequests > 0 ? totalErrors / totalRequests : 0;
    const cacheHitRate =
      totalCacheHits + totalCacheMisses > 0
        ? totalCacheHits / (totalCacheHits + totalCacheMisses)
        : 0;

    return {
      totalRequests,
      averageResponseTime,
      errorRate,
      cacheHitRate,
      activeConnections: this.getLatestGaugeValue("active_connections") || 0,
      memoryUsage: process.memoryUsage(),
      uptime: process.uptime(),
    };
  }

  /**
   * Get metrics for a specific name
   */
  getMetrics(name: string, tags?: Record<string, string>): Metric[] {
    const metrics = this.metrics.get(name) || [];

    if (!tags) {
      return metrics;
    }

    return metrics.filter((metric) => {
      if (!metric.tags) return false;
      return Object.entries(tags).every(
        ([key, value]) => metric.tags![key] === value
      );
    });
  }

  /**
   * Get recent alerts
   */
  getAlerts(limit: number = 50): PerformanceAlert[] {
    return this.alerts
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  /**
   * Clear old metrics to prevent memory leaks
   */
  cleanup(): void {
    const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago

    for (const [name, metrics] of this.metrics.entries()) {
      const filteredMetrics = metrics
        .filter((m) => m.timestamp > cutoffTime)
        .slice(-this.maxMetricsPerType);

      this.metrics.set(name, filteredMetrics);
    }

    // Clean old alerts
    this.alerts = this.alerts
      .filter((alert) => alert.timestamp > cutoffTime)
      .slice(-100);
  }

  /**
   * Record a metric
   */
  private recordMetric(metric: Metric): void {
    if (!this.metrics.has(metric.name)) {
      this.metrics.set(metric.name, []);
    }

    const metrics = this.metrics.get(metric.name)!;
    metrics.push(metric);

    // Keep only recent metrics
    if (metrics.length > this.maxMetricsPerType) {
      metrics.shift();
    }
  }

  /**
   * Get metric values
   */
  private getMetricValues(
    name: string,
    tags?: Record<string, string>
  ): Metric[] {
    return this.getMetrics(name, tags);
  }

  /**
   * Get latest gauge value
   */
  private getLatestGaugeValue(name: string): number | null {
    const metrics = this.getMetrics(name);
    const gaugeMetrics = metrics.filter((m) => m.type === "gauge");

    if (gaugeMetrics.length === 0) return null;

    return gaugeMetrics[gaugeMetrics.length - 1].value;
  }

  /**
   * Check for response time alerts
   */
  private checkResponseTimeAlert(responseTime: number): void {
    if (responseTime > this.alertThresholds.responseTime) {
      this.createAlert(
        "response_time",
        this.alertThresholds.responseTime,
        responseTime,
        "high"
      );
    }
  }

  /**
   * Create performance alert
   */
  private createAlert(
    metric: string,
    threshold: number,
    currentValue: number,
    severity: PerformanceAlert["severity"]
  ): void {
    this.alerts.push({
      metric,
      threshold,
      currentValue,
      severity,
      timestamp: new Date(),
    });
  }

  /**
   * Start collecting system metrics
   */
  private startSystemMetricsCollection(): void {
    setInterval(() => {
      const memUsage = process.memoryUsage();

      this.gauge("memory_used", memUsage.heapUsed);
      this.gauge("memory_total", memUsage.heapTotal);
      this.gauge("memory_external", memUsage.external);
      this.gauge("uptime", process.uptime());

      // Check memory usage alert
      const memoryUsageRatio = memUsage.heapUsed / memUsage.heapTotal;
      if (memoryUsageRatio > this.alertThresholds.memoryUsage) {
        this.createAlert(
          "memory_usage",
          this.alertThresholds.memoryUsage,
          memoryUsageRatio,
          "medium"
        );
      }

      // Cleanup old metrics
      this.cleanup();
    }, 30000); // Every 30 seconds
  }
}

// Global instance
export const metricsService = MetricsService.getInstance();
