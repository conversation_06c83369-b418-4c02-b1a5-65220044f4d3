import { NextRequest, NextResponse } from "next/server";
import { container, SERVICE_IDENTIFIERS } from "../di-container";
import type { SettingService } from "../services/setting.service";
import { initializeSystem } from "../initialize";

// GET /api/settings - L<PERSON>y settings công khai (không cần auth)
export async function GET(_request: NextRequest) {
  try {
    // Ensure system is initialized
    initializeSystem();

    const settingService = container.resolve<SettingService>(
      SERVICE_IDENTIFIERS.SETTING_SERVICE
    );

    // Get public settings from service
    const settingsObject = await settingService.getPublicSettings();

    return NextResponse.json(settingsObject);
  } catch (error) {
    console.error("Get public settings error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy cài đặt" },
      { status: 500 }
    );
  }
}
