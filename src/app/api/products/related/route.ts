import { NextRequest, NextResponse } from "next/server";
import { container, SERVICE_IDENTIFIERS } from "@/app/api/di-container";
import { NotFoundError } from "@/app/api/repositories/base.repository";
import { ProductService } from "@/app/api/services/product.service";

// GET /api/products/related?productId=xxx - L<PERSON>y sản phẩm liên quan
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const productId = searchParams.get("productId");
    const limit = parseInt(searchParams.get("limit") || "4");

    if (!productId) {
      return NextResponse.json(
        { error: "productId là bắt buộc" },
        { status: 400 }
      );
    }

    // Get ProductService from DI container
    const productService = container.resolve<ProductService>(
      SERVICE_IDENTIFIERS.PRODUCT_SERVICE
    );

    // Get related products using service
    const relatedProducts = await productService.getRelatedProducts(
      productId,
      limit
    );

    // Calculate average rating for each product
    const productsWithRating = relatedProducts.map((product: any) => {
      const avgRating =
        product.reviews && product.reviews.length > 0
          ? product.reviews.reduce(
              (sum: number, review: any) => sum + review.rating,
              0
            ) / product.reviews.length
          : 0;

      return {
        ...product,
        avgRating: Math.round(avgRating * 10) / 10,
        reviewCount: product._count?.reviews || 0,
        reviews: undefined, // Remove reviews from response
      };
    });

    return NextResponse.json({
      data: productsWithRating,
      total: productsWithRating.length,
    });
  } catch (error) {
    if (error instanceof NotFoundError) {
      return NextResponse.json(
        { error: "Không tìm thấy sản phẩm" },
        { status: 404 }
      );
    }

    console.error("Get related products error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy sản phẩm liên quan" },
      { status: 500 }
    );
  }
}
