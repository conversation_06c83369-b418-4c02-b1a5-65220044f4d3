import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { adminAuthOptions } from "@/lib/admin-auth";
import { container, SERVICE_IDENTIFIERS } from "../../../di-container";
import { NotificationService } from "../../../services/notification.service";
import { initializeSystem } from "../../../initialize";
import { UserRole } from "@/app/models/common.model";

// GET /api/admin/notifications/[id] - Get specific notification
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.type !== "admin") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    initializeSystem();
    const notificationService =
      container.resolve<NotificationService>(SERVICE_IDENTIFIERS.NOTIFICATION_SERVICE);

    const adminUser = {
      id: session.user.id,
      email: session.user.email || "",
      name: session.user.name || "",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      role: (session.user.role || "ADMIN") as UserRole,
    };

    const notification = await notificationService.getNotificationById(
      params.id,
      adminUser
    );

    return NextResponse.json(notification);
  } catch (error) {
    console.error("Get notification error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy thông báo" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/notifications/[id] - Update notification (mark as read)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.type !== "admin") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { isRead } = body;

    initializeSystem();
    const notificationService =
      container.resolve<NotificationService>(SERVICE_IDENTIFIERS.NOTIFICATION_SERVICE);

    const adminUser = {
      id: session.user.id,
      email: session.user.email || "",
      name: session.user.name || "",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      role: (session.user.role || "ADMIN") as UserRole,
    };

    const updatedNotification = await notificationService.updateNotification(
      params.id,
      {
        isRead: isRead,
        readAt: isRead ? new Date() : undefined,
      },
      adminUser
    );

    return NextResponse.json({
      message: "Cập nhật thông báo thành công",
      notification: updatedNotification,
    });
  } catch (error) {
    console.error("Update notification error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi cập nhật thông báo" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/notifications/[id] - Delete notification
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.type !== "admin") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    // Only ADMIN can delete notifications
    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Chỉ Admin mới có quyền xóa thông báo" },
        { status: 403 }
      );
    }

    initializeSystem();
    const notificationService =
      container.resolve<NotificationService>(SERVICE_IDENTIFIERS.NOTIFICATION_SERVICE);

    const adminUser = {
      id: session.user.id,
      email: session.user.email || "",
      name: session.user.name || "",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      role: (session.user.role || "ADMIN") as UserRole,
    };

    await notificationService.deleteNotification(params.id, adminUser);

    return NextResponse.json({
      message: "Xóa thông báo thành công",
    });
  } catch (error) {
    console.error("Delete notification error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi xóa thông báo" },
      { status: 500 }
    );
  }
}
