import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { adminAuthOptions } from "@/lib/admin-auth";
import { z } from "zod";
import { container, SERVICE_IDENTIFIERS } from "@/app/api/di-container";
import type { CategoryService } from "@/app/api/services/category.service";

const updateCategorySchema = z.object({
  name: z.string().min(1, "Tên danh mục không được để trống").optional(),
  description: z.string().optional(),
  imageId: z.string().optional().nullable(),
  parentId: z.string().optional().nullable(),
  slug: z.string().optional(),
});

// GET /api/admin/categories/[id] - L<PERSON>y chi tiết danh mục
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "<PERSON>hông có quyền truy cập" },
        { status: 403 }
      );
    }

    // Await params before accessing properties (Next.js 15 requirement)
    const { id } = await params;

    const categoryService =
      container.resolve<CategoryService>(SERVICE_IDENTIFIERS.CATEGORY_SERVICE);

    // Create admin user entity for request
    const adminUserEntity = {
      id: "admin",
      email: "<EMAIL>",
      name: "Admin User",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    } as any;

    const category = await categoryService.getCategoryById(id, adminUserEntity);

    if (!category) {
      return NextResponse.json(
        { error: "Không tìm thấy danh mục" },
        { status: 404 }
      );
    }

    return NextResponse.json(category);
  } catch (error) {
    console.error("Get category error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy thông tin danh mục" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/categories/[id] - Cập nhật danh mục (Admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const data = updateCategorySchema.parse(body);

    const categoryService =
      container.resolve<CategoryService>(SERVICE_IDENTIFIERS.CATEGORY_SERVICE);

    // Create admin user entity for audit
    const adminUserEntity = {
      id: "admin",
      email: "<EMAIL>",
      name: "Admin User",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    } as any;

    const category = await categoryService.updateCategory(
      params.id,
      {
        ...data,
        parentId: data.parentId || undefined,
      },
      adminUserEntity
    );

    return NextResponse.json({
      message: "Cập nhật danh mục thành công",
      category,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error("Update category error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi cập nhật danh mục" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/categories/[id] - Xóa danh mục (Admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    const categoryService =
      container.resolve<CategoryService>(SERVICE_IDENTIFIERS.CATEGORY_SERVICE);

    // Create admin user entity for audit
    const adminUserEntity = {
      id: "admin",
      email: "<EMAIL>",
      name: "Admin User",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    } as any;

    // Delete category using service
    await categoryService.deleteCategory(params.id, adminUserEntity);

    return NextResponse.json({
      message: "Xóa danh mục thành công",
    });
  } catch (error) {
    console.error("Delete category error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi xóa danh mục" },
      { status: 500 }
    );
  }
}
