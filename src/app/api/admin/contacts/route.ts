import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { container, SERVICE_IDENTIFIERS } from "@/app/api/di-container";
import type { ContactService } from "@/app/api/services/contact.service";
import { verifyAdminAuth } from "../../utils/admin-auth";

const contactQuerySchema = z.object({
  page: z.string().optional().default("1"),
  limit: z.string().optional().default("10"),
  status: z
    .enum([
      "NEW",
      "OPEN",
      "IN_PROGRESS",
      "WAITING_CUSTOMER",
      "RESOLVED",
      "CLOSED",
      "SPAM",
    ])
    .optional(),
  priority: z.enum(["LOW", "NORMAL", "HIGH", "URGENT"]).optional(),
  search: z.string().optional(),
  assignedTo: z.string().optional(),
  sortBy: z
    .enum(["createdAt", "updatedAt", "name", "priority", "status"])
    .optional()
    .default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).optional().default("desc"),
});

const contactUpdateSchema = z.object({
  status: z
    .enum([
      "NEW",
      "OPEN",
      "IN_PROGRESS",
      "WAITING_CUSTOMER",
      "RESOLVED",
      "CLOSED",
      "SPAM",
    ])
    .optional(),
  priority: z.enum(["LOW", "NORMAL", "HIGH", "URGENT"]).optional(),
  assignedTo: z.string().nullable().optional(),
  responseMessage: z.string().optional(),
  tags: z.array(z.string()).optional(),
});

// GET /api/admin/contacts - List contacts with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const query = contactQuerySchema.parse(Object.fromEntries(searchParams));

    const page = parseInt(query.page);
    const limit = parseInt(query.limit);

    const contactService = container.resolve<ContactService>(SERVICE_IDENTIFIERS.CONTACT_SERVICE);

    // Create admin user entity for request
    const adminUserEntity = {
      id: "admin",
      email: "<EMAIL>",
      name: "Admin User",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    } as any;

    // Get contacts with pagination using service
    const result = await contactService.getContacts(
      {
        page,
        limit,
        search: query.search,
        filters: {
          status: query.status as any,
          priority: query.priority as any,
          assignedTo: query.assignedTo,
        },
        sortBy: query.sortBy,
        sortOrder: query.sortOrder as "asc" | "desc",
      },
      adminUserEntity
    );

    const totalPages = Math.ceil(result.total / limit);

    return NextResponse.json({
      contacts: result.data,
      pagination: {
        page,
        limit,
        total: result.total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid query parameters", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error fetching contacts:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/admin/contacts - Create a new contact (admin only)
export async function POST(request: NextRequest) {
  try {
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: 401 });
    }

    const body = await request.json();
    const contactData = z
      .object({
        name: z.string().min(1),
        email: z.string().email(),
        phone: z.string().optional(),
        company: z.string().optional(),
        service: z.string().optional(),
        subject: z.string().min(1),
        message: z.string().min(1),
        status: z
          .enum([
            "NEW",
            "OPEN",
            "IN_PROGRESS",
            "WAITING_CUSTOMER",
            "RESOLVED",
            "CLOSED",
            "SPAM",
          ])
          .optional()
          .default("NEW"),
        priority: z
          .enum(["LOW", "NORMAL", "HIGH", "URGENT"])
          .optional()
          .default("NORMAL"),
        source: z.string().optional().default("admin"),
        assignedTo: z.string().optional(),
        tags: z.array(z.string()).optional().default([]),
      })
      .parse(body);

    const contactService = container.resolve<ContactService>(SERVICE_IDENTIFIERS.CONTACT_SERVICE);

    // Create admin user entity for audit
    const adminUserEntity = {
      id: "admin",
      email: "<EMAIL>",
      name: "Admin User",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    } as any;

    const contact = await contactService.createContact(
      {
        ...contactData,
        priority: contactData.priority as any,
      },
      adminUserEntity
    );

    return NextResponse.json(contact, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid contact data", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error creating contact:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
