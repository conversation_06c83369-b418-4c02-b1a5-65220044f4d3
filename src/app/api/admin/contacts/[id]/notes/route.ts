import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { verifyAdminAuth } from "@/lib/auth/admin-auth";
import { container, SERVICE_IDENTIFIERS } from "../../../../di-container";
import type { ContactService } from "../../../../services/contact.service";
import { initializeSystem } from "../../../../initialize";
import { UserRole } from "@/app/models/common.model";

const noteCreateSchema = z.object({
  note: z.string().min(1, "Note cannot be empty"),
  isInternal: z.boolean().optional().default(true),
});

// GET /api/admin/contacts/[id]/notes - Get notes for a contact
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await verifyAdminAuth();
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Initialize system and get service
    initializeSystem();
    const contactService = container.resolve<ContactService>(SERVICE_IDENTIFIERS.CONTACT_SERVICE);

    // Create admin user entity
    const adminUser = {
      id: session.user.id,
      email: session.user.email || "",
      name: session.user.name || "",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      role: session.user.role as UserRole,
    };

    // Check if contact exists and get notes
    try {
      const contact = await contactService.getContactById(params.id, adminUser);
      if (!contact) {
        return NextResponse.json(
          { error: "Contact not found" },
          { status: 404 }
        );
      }

      const notes = await contactService.getContactNotes(params.id, adminUser);
      return NextResponse.json(notes);
    } catch (_error) {
      return NextResponse.json({ error: "Contact not found" }, { status: 404 });
    }
  } catch (error) {
    console.error("Error fetching contact notes:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/admin/contacts/[id]/notes - Add a note to a contact
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await verifyAdminAuth();
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const noteData = noteCreateSchema.parse(body);

    // Initialize system and get service
    initializeSystem();
    const contactService = container.resolve<ContactService>(SERVICE_IDENTIFIERS.CONTACT_SERVICE);

    // Create admin user entity
    const adminUserEntity = {
      id: session.user.id,
      email: session.user.email || "",
      name: session.user.name || "",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      role: session.user.role as UserRole,
    };

    // Create note using service
    const note = await contactService.createContactNote(
      params.id,
      {
        note: noteData.note,
        isInternal: noteData.isInternal,
      },
      adminUserEntity
    );

    return NextResponse.json(note, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid note data", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error creating contact note:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
