import { NextRequest, NextResponse } from "next/server";
import { verifyAdminAuth } from "../../utils/admin-auth";

// GET /api/admin/payment-gateways - Get payment gateways
export async function GET(request: NextRequest) {
  try {
    const authResult = await verifyAdminAuth(request);

    if (!authResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: authResult.error || "Không có quyền truy cập",
        },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const search = searchParams.get("search");

    // Mock data for now - TODO: Implement PaymentGatewayService
    const mockGateways = [
      {
        id: "1",
        name: "VNPay",
        provider: "VNPAY",
        isActive: true,
        isDefault: true,
        config: {},
        credentials: {},
        supportedMethods: ["CARD", "BANK_TRANSFER"],
        fees: { percentage: 2.5, fixed: 0 },
        limits: { min: 10000, max: ******** },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        transactions: [],
      },
      {
        id: "2",
        name: "MoMo",
        provider: "MOMO",
        isActive: true,
        isDefault: false,
        config: {},
        credentials: {},
        supportedMethods: ["WALLET"],
        fees: { percentage: 2.0, fixed: 0 },
        limits: { min: 10000, max: ******** },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        transactions: [],
      },
    ];

    // Filter by search if provided
    let filteredGateways = mockGateways;
    if (search) {
      filteredGateways = mockGateways.filter((gateway) =>
        gateway.name.toLowerCase().includes(search.toLowerCase())
      );
    }

    // Pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedGateways = filteredGateways.slice(startIndex, endIndex);

    return NextResponse.json({
      success: true,
      data: paginatedGateways,
      pagination: {
        page,
        limit,
        total: filteredGateways.length,
        pages: Math.ceil(filteredGateways.length / limit),
      },
    });
  } catch (error) {
    console.error("Get payment gateways error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi lấy danh sách cổng thanh toán" },
      { status: 500 }
    );
  }
}

// POST /api/admin/payment-gateways - Create payment gateway
export async function POST(request: NextRequest) {
  try {
    const authResult = await verifyAdminAuth(request);

    if (!authResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: authResult.error || "Không có quyền truy cập",
        },
        { status: 403 }
      );
    }

    const body = await request.json();

    // TODO: Implement PaymentGatewayService
    const newGateway = {
      id: Date.now().toString(),
      ...body,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      transactions: [],
    };

    return NextResponse.json({
      success: true,
      data: newGateway,
      message: "Cổng thanh toán đã được tạo thành công",
    });
  } catch (error) {
    console.error("Create payment gateway error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi tạo cổng thanh toán" },
      { status: 500 }
    );
  }
}
