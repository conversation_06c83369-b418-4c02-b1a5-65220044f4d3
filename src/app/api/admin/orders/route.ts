import { NextRequest, NextResponse } from "next/server";
import { container, SERVICE_IDENTIFIERS } from "@/app/api/di-container";
import type { OrderService } from "@/app/api/services/order.service";
import { jwtVerify } from "jose";
import { initializeSystem } from "@/app/api/initialize";

// Helper function to verify admin JWT token
async function verifyAdminToken(request: NextRequest) {
  try {
    const adminToken = request.cookies.get("admin-session")?.value;
    if (!adminToken) return null;

    const secret = new TextEncoder().encode(
      process.env.NEXTAUTH_SECRET || "fallback-secret"
    );
    const { payload } = await jwtVerify(adminToken, secret);
    return payload;
  } catch {
    return null;
  }
}

// GET /api/admin/orders - <PERSON><PERSON><PERSON> danh sách đơn hàng cho admin
export async function GET(request: NextRequest) {
  try {
    // Initialize system first
    initializeSystem();

    const adminToken = await verifyAdminToken(request);

    if (
      !adminToken ||
      (adminToken.role !== "ADMIN" && adminToken.role !== "MODERATOR")
    ) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const search = searchParams.get("search");
    const status = searchParams.get("status");
    const _paymentMethod = searchParams.get("paymentMethod");
    const paymentStatus = searchParams.get("paymentStatus");

    const orderService = container.resolve<OrderService>(
      SERVICE_IDENTIFIERS.ORDER_SERVICE
    );

    // Get orders with pagination using service
    const filters = {
      page,
      limit,
      search: search || undefined,
      status: status as any,
      paymentStatus: paymentStatus as any,
      sortBy: "createdAt",
      sortOrder: "desc" as const,
    };

    const result = await orderService.searchOrders(filters);

    return NextResponse.json({
      success: true,
      data: result.data,
      pagination: {
        page,
        limit,
        total: result.total,
        pages: Math.ceil(result.total / limit),
      },
    });
  } catch (error) {
    console.error("Get admin orders error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi lấy danh sách đơn hàng" },
      { status: 500 }
    );
  }
}
