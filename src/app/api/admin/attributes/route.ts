import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { jwtVerify } from "jose";
import { container, SERVICE_IDENTIFIERS } from "../../di-container";
import type { AttributeService } from "../../services/attribute.service";
import { initializeSystem } from "../../initialize";
import {
  AttributeType,
  CreateAttributeData,
} from "../../../models/attribute.model";
import { UserEntity } from "../../../models/user.model";
import { UserRole } from "../../../models/common.model";

// Helper function to verify admin authentication
async function verifyAdminAuth(request: NextRequest) {
  try {
    const adminToken = request.cookies.get("admin-session")?.value;

    if (!adminToken) {
      return null;
    }

    const secret = new TextEncoder().encode(
      process.env.NEXTAUTH_SECRET || "fallback-secret"
    );

    const { payload } = await jwtVerify(adminToken, secret);

    if (payload.role !== "ADMIN" && payload.role !== "MODERATOR") {
      return null;
    }

    return {
      id: payload.id as string,
      role: payload.role as string,
      type: "admin" as const,
    };
  } catch {
    return null;
  }
}

// Validation schemas
const attributeSchema = z.object({
  name: z.string().min(1, "Tên thuộc tính là bắt buộc"),
  slug: z.string().optional(),
  description: z.string().optional(),
  type: z.enum([
    "TEXT",
    "NUMBER",
    "COLOR",
    "SIZE",
    "BOOLEAN",
    "SELECT",
    "MULTI_SELECT",
  ]),
  isRequired: z.boolean().default(false),
  isFilterable: z.boolean().default(true),
  sortOrder: z.number().default(0),
  values: z
    .array(
      z.object({
        value: z.string().min(1),
        slug: z.string().optional(),
        sortOrder: z.number().default(0),
      })
    )
    .optional(),
});

// GET /api/admin/attributes - Lấy danh sách attributes
export async function GET(request: NextRequest) {
  try {
    const adminUser = await verifyAdminAuth(request);

    if (!adminUser) {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    // Initialize system and get service
    initializeSystem();
    const attributeService = container.resolve<AttributeService>(
      SERVICE_IDENTIFIERS.ATTRIBUTE_SERVICE
    );

    const { searchParams } = new URL(request.url);
    const search = searchParams.get("search") || "";
    const type = searchParams.get("type") || "";
    const isRequired = searchParams.get("isRequired");
    const isFilterable = searchParams.get("isFilterable");
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const sortBy = searchParams.get("sortBy") || "sortOrder";
    const sortOrder = searchParams.get("sortOrder") || "asc";

    // Build where clause
    const where: any = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
      ];
    }

    if (type) {
      where.type = type;
    }

    if (isRequired !== null) {
      where.isRequired = isRequired === "true";
    }

    if (isFilterable !== null) {
      where.isFilterable = isFilterable === "true";
    }

    // Get attributes with pagination
    const result = await attributeService.getAttributes({
      page,
      limit,
      search,
      type,
      isRequired:
        isRequired === "true"
          ? true
          : isRequired === "false"
            ? false
            : undefined,
      isFilterable:
        isFilterable === "true"
          ? true
          : isFilterable === "false"
            ? false
            : undefined,
      sortBy,
      sortOrder: sortOrder as "asc" | "desc",
    });

    const { data: attributes, total } = result;

    return NextResponse.json({
      success: true,
      data: attributes,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Get attributes error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy danh sách thuộc tính" },
      { status: 500 }
    );
  }
}

// POST /api/admin/attributes - Tạo attribute mới
export async function POST(request: NextRequest) {
  try {
    const adminUser = await verifyAdminAuth(request);

    if (!adminUser) {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    // Initialize system and get service
    initializeSystem();
    const attributeService = container.resolve<AttributeService>(
      SERVICE_IDENTIFIERS.ATTRIBUTE_SERVICE
    );

    const body = await request.json();
    const validatedData = attributeSchema.parse(body);

    // Generate slug if not provided
    if (!validatedData.slug) {
      validatedData.slug = validatedData.name
        .toLowerCase()
        .replace(/\s+/g, "-")
        .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, "a")
        .replace(/[èéẹẻẽêềếệểễ]/g, "e")
        .replace(/[ìíịỉĩ]/g, "i")
        .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, "o")
        .replace(/[ùúụủũưừứựửữ]/g, "u")
        .replace(/[ỳýỵỷỹ]/g, "y")
        .replace(/[đ]/g, "d")
        .replace(/[^a-z0-9-]/g, "");
    }

    // Check if slug already exists
    const existingAttribute = await attributeService.getAttributeBySlug(
      validatedData.slug!
    );

    if (existingAttribute) {
      return NextResponse.json({ error: "Slug đã tồn tại" }, { status: 400 });
    }

    // Create attribute with values
    const createData: CreateAttributeData = {
      name: validatedData.name,
      slug: validatedData.slug,
      type: validatedData.type as AttributeType,
      values: validatedData.values?.map((v) => v.value), // Convert to string array
      isRequired: validatedData.isRequired,
      isFilterable: validatedData.isFilterable,
      sortOrder: validatedData.sortOrder,
    };

    // Convert adminUser to UserEntity
    const userEntity: UserEntity = {
      id: adminUser.id,
      email: adminUser.id, // Use id as email placeholder
      name: "Admin User",
      isActive: true,
      role: UserRole.ADMIN,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const attribute = await attributeService.createAttribute(
      createData,
      userEntity
    );

    // Fetch the created attribute with values
    const createdAttribute = await attributeService.getAttributeById(
      attribute.id
    );

    return NextResponse.json({
      success: true,
      data: createdAttribute,
      message: "Thuộc tính đã được tạo thành công",
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Dữ liệu không hợp lệ", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Create attribute error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi tạo thuộc tính" },
      { status: 500 }
    );
  }
}
