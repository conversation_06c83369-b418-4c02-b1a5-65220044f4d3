import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { jwtVerify } from "jose";
import { container, SERVICE_IDENTIFIERS } from "../../../../di-container";
import type { AttributeRepository } from "../../../../repositories/attribute.repository";
import { initializeSystem } from "../../../../initialize";

// Helper function to verify admin authentication
async function verifyAdminAuth(request: NextRequest) {
  try {
    const adminToken = request.cookies.get("admin-session")?.value;

    if (!adminToken) {
      return null;
    }

    const secret = new TextEncoder().encode(
      process.env.NEXTAUTH_SECRET || "fallback-secret"
    );

    const { payload } = await jwtVerify(adminToken, secret);

    if (payload.role !== "ADMIN" && payload.role !== "MODERATOR") {
      return null;
    }

    return {
      id: payload.id as string,
      role: payload.role as string,
      type: "admin" as const,
    };
  } catch {
    return null;
  }
}

// Validation schema for attribute value
const attributeValueSchema = z.object({
  value: z.string().min(1, "Giá trị là bắt buộc"),
  slug: z.string().optional(),
  sortOrder: z.number().default(0),
});

// GET /api/admin/attributes/[id]/values - Lấy danh sách values của attribute
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const adminUser = await verifyAdminAuth(request);

    if (!adminUser) {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    // Await params before accessing properties (Next.js 15 requirement)
    const { id } = await params;

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "50");
    const search = searchParams.get("search") || "";
    const sortBy = searchParams.get("sortBy") || "sortOrder";
    const sortOrder = searchParams.get("sortOrder") || "asc";

    const skip = (page - 1) * limit;

    // Initialize system and get repository
    initializeSystem();
    const attributeRepository = container.resolve<AttributeRepository>(
      SERVICE_IDENTIFIERS.ATTRIBUTE_REPOSITORY
    );

    // Check if attribute exists
    const attribute = await attributeRepository.findById(id);

    if (!attribute) {
      return NextResponse.json(
        { error: "Không tìm thấy thuộc tính" },
        { status: 404 }
      );
    }

    // Build where clause
    const where: any = {
      attributeId: id,
    };

    if (search) {
      where.value = {
        contains: search,
        mode: "insensitive",
      };
    }

    // Get values with pagination - simplified approach
    const allValues = await attributeRepository.getAttributeValues(id);

    // Apply search filter if needed
    let filteredValues = allValues;
    if (search) {
      filteredValues = allValues.filter((v) =>
        v.value.toLowerCase().includes(search.toLowerCase())
      );
    }

    // Apply sorting
    filteredValues.sort((a, b) => {
      if (sortBy === "sortOrder") {
        return sortOrder === "asc"
          ? a.sortOrder - b.sortOrder
          : b.sortOrder - a.sortOrder;
      } else if (sortBy === "value") {
        return sortOrder === "asc"
          ? a.value.localeCompare(b.value)
          : b.value.localeCompare(a.value);
      }
      return 0;
    });

    // Apply pagination
    const total = filteredValues.length;
    const values = filteredValues.slice(skip, skip + limit);

    const pagination = {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit),
    };

    return NextResponse.json({
      data: values,
      pagination,
    });
  } catch (error) {
    console.error("Error fetching attribute values:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi tải danh sách giá trị" },
      { status: 500 }
    );
  }
}

// POST /api/admin/attributes/[id]/values - Tạo value mới cho attribute
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const adminUser = await verifyAdminAuth(request);

    if (!adminUser) {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    // Await params before accessing properties (Next.js 15 requirement)
    const { id } = await params;

    // Initialize system and get repository
    initializeSystem();
    const attributeRepository = container.resolve<AttributeRepository>(
      SERVICE_IDENTIFIERS.ATTRIBUTE_REPOSITORY
    );

    const body = await request.json();
    const validatedData = attributeValueSchema.parse(body);

    // Check if attribute exists
    const attribute = await attributeRepository.findById(id);

    if (!attribute) {
      return NextResponse.json(
        { error: "Không tìm thấy thuộc tính" },
        { status: 404 }
      );
    }

    // Generate slug if not provided
    if (!validatedData.slug) {
      validatedData.slug = validatedData.value
        .toLowerCase()
        .replace(/\s+/g, "-")
        .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, "a")
        .replace(/[èéẹẻẽêềếệểễ]/g, "e")
        .replace(/[ìíịỉĩ]/g, "i")
        .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, "o")
        .replace(/[ùúụủũưừứựửữ]/g, "u")
        .replace(/[ỳýỵỷỹ]/g, "y")
        .replace(/[đ]/g, "d")
        .replace(/[^a-z0-9-]/g, "");
    }

    // Check for duplicate value
    const allAttributeValues = await attributeRepository.getAttributeValues(id);
    const existingValue = allAttributeValues.find(
      (v) => v.value === validatedData.value || v.slug === validatedData.slug
    );

    if (existingValue) {
      return NextResponse.json(
        { error: "Giá trị hoặc slug đã tồn tại" },
        { status: 400 }
      );
    }

    // Create attribute value
    const attributeValue = await attributeRepository.createAttributeValue({
      attributeId: id,
      value: validatedData.value,
      slug: validatedData.slug,
      sortOrder: validatedData.sortOrder,
    });

    return NextResponse.json(attributeValue, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Dữ liệu không hợp lệ", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error creating attribute value:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi tạo giá trị" },
      { status: 500 }
    );
  }
}
