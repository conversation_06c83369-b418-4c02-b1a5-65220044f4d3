import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { adminAuthOptions } from "@/lib/admin-auth";
import { z } from "zod";
import { container, SERVICE_IDENTIFIERS } from "../../../../../di-container";
import type { AttributeRepository } from "../../../../../repositories/attribute.repository";
import { initializeSystem } from "../../../../../initialize";

// Validation schema for updating attribute value
const updateAttributeValueSchema = z.object({
  value: z.string().min(1, "Giá trị là bắt buộc").optional(),
  slug: z.string().optional(),
  sortOrder: z.number().optional(),
});

// GET /api/admin/attributes/[id]/values/[valueId] - <PERSON><PERSON><PERSON> thông tin chi tiết value
export async function GET(
  _request: NextRequest,
  { params }: { params: { id: string; valueId: string } }
) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    // Await params before accessing properties (Next.js 15 requirement)
    const { id, valueId } = await params;

    // Initialize system and get repository
    initializeSystem();
    const attributeRepository = container.resolve<AttributeRepository>(
      SERVICE_IDENTIFIERS.ATTRIBUTE_REPOSITORY
    );

    // Find attribute value by ID and check if it belongs to the attribute
    const allValues = await attributeRepository.getAttributeValues(id);
    const attributeValue = allValues.find((v) => v.id === valueId);

    if (attributeValue) {
      // Get the attribute info
      const attribute = await attributeRepository.findById(id);
      // Add attribute info to the value
      (attributeValue as any).attribute = attribute;
    }

    if (!attributeValue) {
      return NextResponse.json(
        { error: "Không tìm thấy giá trị thuộc tính" },
        { status: 404 }
      );
    }

    return NextResponse.json(attributeValue);
  } catch (error) {
    console.error("Error fetching attribute value:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi tải thông tin giá trị" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/attributes/[id]/values/[valueId] - Cập nhật value
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string; valueId: string } }
) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    // Await params before accessing properties (Next.js 15 requirement)
    const { id, valueId } = await params;

    // Initialize system and get repository
    initializeSystem();
    const attributeRepository = container.resolve<AttributeRepository>(
      SERVICE_IDENTIFIERS.ATTRIBUTE_REPOSITORY
    );

    const body = await request.json();
    const validatedData = updateAttributeValueSchema.parse(body);

    // Check if attribute value exists
    const allValues = await attributeRepository.getAttributeValues(id);
    const existingValue = allValues.find((v) => v.id === valueId);

    if (!existingValue) {
      return NextResponse.json(
        { error: "Không tìm thấy giá trị thuộc tính" },
        { status: 404 }
      );
    }

    // Auto-generate slug if value is updated but slug is not provided
    if (validatedData.value && !validatedData.slug) {
      validatedData.slug = validatedData.value
        .toLowerCase()
        .replace(/\s+/g, "-")
        .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, "a")
        .replace(/[èéẹẻẽêềếệểễ]/g, "e")
        .replace(/[ìíịỉĩ]/g, "i")
        .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, "o")
        .replace(/[ùúụủũưừứựửữ]/g, "u")
        .replace(/[ỳýỵỷỹ]/g, "y")
        .replace(/[đ]/g, "d")
        .replace(/[^a-z0-9-]/g, "");
    }

    // Check for duplicate value or slug (excluding current value)
    if (validatedData.value || validatedData.slug) {
      const duplicateCheck: any = {
        attributeId: id,
        id: { not: valueId },
      };

      const orConditions = [];
      if (validatedData.value) {
        orConditions.push({ value: validatedData.value });
      }
      if (validatedData.slug) {
        orConditions.push({ slug: validatedData.slug });
      }

      if (orConditions.length > 0) {
        duplicateCheck.OR = orConditions;

        // Check for duplicates in the current attribute values
        const duplicateValue = allValues.find(
          (v) =>
            v.id !== valueId &&
            ((validatedData.value && v.value === validatedData.value) ||
              (validatedData.slug && v.slug === validatedData.slug))
        );

        if (duplicateValue) {
          return NextResponse.json(
            { error: "Giá trị hoặc slug đã tồn tại" },
            { status: 400 }
          );
        }
      }
    }

    // Update attribute value
    const updatedValue = await attributeRepository.updateAttributeValue(
      valueId,
      validatedData
    );

    return NextResponse.json(updatedValue);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Dữ liệu không hợp lệ", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error updating attribute value:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi cập nhật giá trị" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/attributes/[id]/values/[valueId] - Xóa value
export async function DELETE(
  _request: NextRequest,
  { params }: { params: { id: string; valueId: string } }
) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    // Await params before accessing properties (Next.js 15 requirement)
    const { id, valueId } = await params;

    // Initialize system and get repository
    initializeSystem();
    const attributeRepository = container.resolve<AttributeRepository>(
      SERVICE_IDENTIFIERS.ATTRIBUTE_REPOSITORY
    );

    // Check if attribute value exists
    const allValues = await attributeRepository.getAttributeValues(id);
    const existingValue = allValues.find((v) => v.id === valueId);

    if (!existingValue) {
      return NextResponse.json(
        { error: "Không tìm thấy giá trị thuộc tính" },
        { status: 404 }
      );
    }

    // Check if value is being used by products (simplified check)
    // Note: We'll skip the usage check for now since we don't have the count
    // In a real implementation, you'd want to add a method to check usage

    // Delete attribute value
    await attributeRepository.deleteAttributeValue(valueId);

    return NextResponse.json({ message: "Xóa giá trị thành công" });
  } catch (error) {
    console.error("Error deleting attribute value:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi xóa giá trị" },
      { status: 500 }
    );
  }
}
