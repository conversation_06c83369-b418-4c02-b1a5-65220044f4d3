import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { jwtVerify } from "jose";
import { container, SERVICE_IDENTIFIERS } from "../../../di-container";
import type { AttributeRepository } from "../../../repositories/attribute.repository";
import { initializeSystem } from "../../../initialize";

// Helper function to verify admin authentication
async function verifyAdminAuth(request: NextRequest) {
  try {
    const adminToken = request.cookies.get("admin-session")?.value;

    if (!adminToken) {
      return null;
    }

    const secret = new TextEncoder().encode(
      process.env.NEXTAUTH_SECRET || "fallback-secret"
    );

    const { payload } = await jwtVerify(adminToken, secret);

    if (payload.role !== "ADMIN" && payload.role !== "MODERATOR") {
      return null;
    }

    return {
      id: payload.id as string,
      role: payload.role as string,
      type: "admin" as const,
    };
  } catch {
    return null;
  }
}

// Validation schema
const updateAttributeSchema = z.object({
  name: z.string().min(1, "Tên thuộc tính là bắt buộc").optional(),
  slug: z.string().optional(),
  description: z.string().optional(),
  type: z
    .enum([
      "TEXT",
      "NUMBER",
      "COLOR",
      "SIZE",
      "BOOLEAN",
      "SELECT",
      "MULTI_SELECT",
    ])
    .optional(),
  isRequired: z.boolean().optional(),
  isFilterable: z.boolean().optional(),
  sortOrder: z.number().optional(),
  values: z
    .array(
      z.object({
        id: z.string().optional(),
        value: z.string().min(1),
        slug: z.string().optional(),
        sortOrder: z.number().default(0),
      })
    )
    .optional(),
});

// GET /api/admin/attributes/[id] - Lấy chi tiết attribute
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const adminUser = await verifyAdminAuth(request);

    if (!adminUser) {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    // Await params before accessing properties (Next.js 15 requirement)
    const { id } = await params;

    // Initialize system and get repository
    initializeSystem();
    const attributeRepository = container.resolve<AttributeRepository>(
      SERVICE_IDENTIFIERS.ATTRIBUTE_REPOSITORY
    );

    const attribute = await attributeRepository.findByIdWithValues(id);

    if (!attribute) {
      return NextResponse.json(
        { error: "Không tìm thấy thuộc tính" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: attribute,
    });
  } catch (error) {
    console.error("Get attribute error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy thông tin thuộc tính" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/attributes/[id] - Cập nhật attribute
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const adminUser = await verifyAdminAuth(request);

    if (!adminUser) {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    // Await params before accessing properties (Next.js 15 requirement)
    const { id } = await params;

    // Initialize system and get repository
    initializeSystem();
    const attributeRepository = container.resolve<AttributeRepository>(
      SERVICE_IDENTIFIERS.ATTRIBUTE_REPOSITORY
    );

    const body = await request.json();
    const validatedData = updateAttributeSchema.parse(body);

    // Check if attribute exists
    const existingAttribute = await attributeRepository.findByIdWithValues(id);

    if (!existingAttribute) {
      return NextResponse.json(
        { error: "Không tìm thấy thuộc tính" },
        { status: 404 }
      );
    }

    // Generate slug if name is updated
    if (validatedData.name && !validatedData.slug) {
      validatedData.slug = validatedData.name
        .toLowerCase()
        .replace(/\s+/g, "-")
        .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, "a")
        .replace(/[èéẹẻẽêềếệểễ]/g, "e")
        .replace(/[ìíịỉĩ]/g, "i")
        .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, "o")
        .replace(/[ùúụủũưừứựửữ]/g, "u")
        .replace(/[ỳýỵỷỹ]/g, "y")
        .replace(/[đ]/g, "d")
        .replace(/[^a-z0-9-]/g, "");
    }

    // Check if slug already exists (if changed)
    if (validatedData.slug && validatedData.slug !== existingAttribute.slug) {
      const slugExists = await attributeRepository.findBySlug(
        validatedData.slug
      );

      if (slugExists) {
        return NextResponse.json({ error: "Slug đã tồn tại" }, { status: 400 });
      }
    }

    // Update attribute
    const updateData: any = {};
    if (validatedData.name) updateData.name = validatedData.name;
    if (validatedData.slug) updateData.slug = validatedData.slug;
    if (validatedData.description !== undefined)
      updateData.description = validatedData.description;
    if (validatedData.type) updateData.type = validatedData.type;
    if (validatedData.isRequired !== undefined)
      updateData.isRequired = validatedData.isRequired;
    if (validatedData.isFilterable !== undefined)
      updateData.isFilterable = validatedData.isFilterable;
    if (validatedData.sortOrder !== undefined)
      updateData.sortOrder = validatedData.sortOrder;

    const updatedAttribute = await attributeRepository.update(id, updateData);

    // Update values if provided
    if (validatedData.values) {
      // Delete existing values that are not in the new list
      const newValueIds = validatedData.values
        .filter((v) => v.id)
        .map((v) => v.id!);

      // Delete existing values that are not in the new list
      for (const existingValue of existingAttribute.values) {
        if (!newValueIds.includes(existingValue.id)) {
          await attributeRepository.deleteAttributeValue(existingValue.id);
        }
      }

      // Update or create values
      for (const valueData of validatedData.values) {
        const valueSlug =
          valueData.slug ||
          valueData.value
            .toLowerCase()
            .replace(/\s+/g, "-")
            .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, "a")
            .replace(/[èéẹẻẽêềếệểễ]/g, "e")
            .replace(/[ìíịỉĩ]/g, "i")
            .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, "o")
            .replace(/[ùúụủũưừứựửữ]/g, "u")
            .replace(/[ỳýỵỷỹ]/g, "y")
            .replace(/[đ]/g, "d")
            .replace(/[^a-z0-9-]/g, "");

        if (valueData.id) {
          // Update existing value
          await attributeRepository.updateAttributeValue(valueData.id, {
            value: valueData.value,
            slug: valueSlug,
            sortOrder: valueData.sortOrder,
          });
        } else {
          // Create new value
          await attributeRepository.createAttributeValue({
            attributeId: id,
            value: valueData.value,
            slug: valueSlug,
            sortOrder: valueData.sortOrder,
          });
        }
      }
    }

    // Fetch the updated attribute with values
    const result = await attributeRepository.findByIdWithValues(
      updatedAttribute.id
    );

    return NextResponse.json({
      success: true,
      data: result,
      message: "Thuộc tính đã được cập nhật thành công",
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Dữ liệu không hợp lệ", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Update attribute error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi cập nhật thuộc tính" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/attributes/[id] - Xóa attribute
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const adminUser = await verifyAdminAuth(request);

    if (!adminUser) {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    // Await params before accessing properties (Next.js 15 requirement)
    const { id } = await params;

    // Initialize system and get repository
    initializeSystem();
    const attributeRepository = container.resolve<AttributeRepository>(
      SERVICE_IDENTIFIERS.ATTRIBUTE_REPOSITORY
    );

    // Check if attribute exists
    const existingAttribute = await attributeRepository.findById(id);

    if (!existingAttribute) {
      return NextResponse.json(
        { error: "Không tìm thấy thuộc tính" },
        { status: 404 }
      );
    }

    // Check if attribute is being used by products
    const isUsed = await attributeRepository.isAttributeUsed(id);
    if (isUsed) {
      return NextResponse.json(
        {
          error: "Không thể xóa thuộc tính đang được sử dụng bởi sản phẩm",
        },
        { status: 400 }
      );
    }

    // Delete attribute (values will be deleted automatically due to cascade)
    await attributeRepository.delete(id);

    return NextResponse.json({
      success: true,
      message: "Thuộc tính đã được xóa thành công",
    });
  } catch (error) {
    console.error("Delete attribute error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi xóa thuộc tính" },
      { status: 500 }
    );
  }
}
