import { NextRequest, NextResponse } from "next/server";
import { verifyAdminAuth } from "../../utils/admin-auth";

// GET /api/admin/payment-transactions - Get payment transactions
export async function GET(request: NextRequest) {
  try {
    const authResult = await verifyAdminAuth(request);

    if (!authResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: authResult.error || "Không có quyền truy cập",
        },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const search = searchParams.get("search");
    const status = searchParams.get("status");
    const gatewayId = searchParams.get("gatewayId");

    // Mock data for now - TODO: Implement PaymentTransactionService
    const mockTransactions = [
      {
        id: "1",
        orderId: "ORDER-001",
        gatewayId: "1",
        gatewayTransactionId: "VNP-123456",
        amount: 500000,
        currency: "VND",
        status: "COMPLETED",
        method: "CARD",
        description: "Thanh toán đơn hàng ORDER-001",
        metadata: {},
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        gateway: {
          id: "1",
          name: "VNPay",
          provider: "VNPAY",
        },
        order: {
          id: "ORDER-001",
          orderNumber: "ORDER-001",
          total: 500000,
        },
      },
      {
        id: "2",
        orderId: "ORDER-002",
        gatewayId: "2",
        gatewayTransactionId: "MOMO-789012",
        amount: 300000,
        currency: "VND",
        status: "PENDING",
        method: "WALLET",
        description: "Thanh toán đơn hàng ORDER-002",
        metadata: {},
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        gateway: {
          id: "2",
          name: "MoMo",
          provider: "MOMO",
        },
        order: {
          id: "ORDER-002",
          orderNumber: "ORDER-002",
          total: 300000,
        },
      },
    ];

    // Filter by search, status, gatewayId if provided
    let filteredTransactions = mockTransactions;
    
    if (search) {
      filteredTransactions = filteredTransactions.filter((transaction) =>
        transaction.orderId.toLowerCase().includes(search.toLowerCase()) ||
        transaction.gatewayTransactionId.toLowerCase().includes(search.toLowerCase())
      );
    }

    if (status) {
      filteredTransactions = filteredTransactions.filter((transaction) =>
        transaction.status === status
      );
    }

    if (gatewayId) {
      filteredTransactions = filteredTransactions.filter((transaction) =>
        transaction.gatewayId === gatewayId
      );
    }

    // Pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedTransactions = filteredTransactions.slice(startIndex, endIndex);

    return NextResponse.json({
      success: true,
      data: paginatedTransactions,
      pagination: {
        page,
        limit,
        total: filteredTransactions.length,
        pages: Math.ceil(filteredTransactions.length / limit),
      },
    });
  } catch (error) {
    console.error("Get payment transactions error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi lấy danh sách giao dịch thanh toán" },
      { status: 500 }
    );
  }
}
