import { NextRequest, NextResponse } from "next/server";
import { container, SERVICE_IDENTIFIERS } from "@/app/api/di-container";
import type { AuditLogService } from "@/app/api/services/audit-log.service";
import { jwtVerify } from "jose";
import type { UserEntity } from "@/app/models/user.model";
import { UserRole } from "@/app/models/common.model";

// Helper function to verify admin JWT token
async function verifyAdminToken(request: NextRequest) {
  try {
    const adminToken = request.cookies.get("admin-session")?.value;
    if (!adminToken) return null;

    const secret = new TextEncoder().encode(
      process.env.NEXTAUTH_SECRET || "fallback-secret"
    );
    const { payload } = await jwtVerify(adminToken, secret);
    return payload;
  } catch {
    return null;
  }
}

interface RouteParams {
  params: {
    id: string;
  };
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    // Check admin authentication
    const adminToken = await verifyAdminToken(request);
    if (!adminToken || adminToken.type !== "admin") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = params;

    // Resolve audit log service
    const auditLogService =
      container.resolve<AuditLogService>(SERVICE_IDENTIFIERS.AUDIT_LOG_SERVICE);

    // Create admin user entity for permission check
    const adminUserEntity: UserEntity = {
      id: adminToken.id as string,
      email: adminToken.email as string,
      name: adminToken.name as string,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      role: adminToken.role as UserRole,
    };

    // Get audit log using service
    const auditLog = await auditLogService.getAuditLogById(id, adminUserEntity);

    if (!auditLog) {
      return NextResponse.json(
        { error: "Audit log not found" },
        { status: 404 }
      );
    }

    // Format the response with additional details
    const formattedAuditLog = {
      ...auditLog,
      formattedCreatedAt: auditLog.createdAt.toLocaleString("vi-VN", {
        timeZone: "Asia/Ho_Chi_Minh",
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
      }),
      changes:
        auditLog.oldValues && auditLog.newValues
          ? getChanges(auditLog.oldValues, auditLog.newValues)
          : null,
    };

    return NextResponse.json({
      data: formattedAuditLog,
    });
  } catch (error) {
    console.error("Error fetching audit log:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Helper function to compare old and new values
function getChanges(oldValues: any, newValues: any) {
  const changes: Array<{
    field: string;
    oldValue: any;
    newValue: any;
    type: "added" | "modified" | "removed";
  }> = [];

  const allKeys = new Set([
    ...Object.keys(oldValues || {}),
    ...Object.keys(newValues || {}),
  ]);

  for (const key of allKeys) {
    const oldValue = oldValues?.[key];
    const newValue = newValues?.[key];

    if (oldValue === undefined && newValue !== undefined) {
      changes.push({
        field: key,
        oldValue: null,
        newValue,
        type: "added",
      });
    } else if (oldValue !== undefined && newValue === undefined) {
      changes.push({
        field: key,
        oldValue,
        newValue: null,
        type: "removed",
      });
    } else if (oldValue !== newValue) {
      changes.push({
        field: key,
        oldValue,
        newValue,
        type: "modified",
      });
    }
  }

  return changes;
}
