import { NextRequest, NextResponse } from "next/server";
import { container, SERVICE_IDENTIFIERS } from "@/app/api/di-container";
import type { AuditLogService } from "@/app/api/services/audit-log.service";
import { z } from "zod";
import { jwtVerify } from "jose";
import type { UserEntity } from "@/app/models/user.model";
import { UserRole } from "@/app/models/common.model";

// Helper function to verify admin JWT token
async function verifyAdminToken(request: NextRequest) {
  try {
    const adminToken = request.cookies.get("admin-session")?.value;
    if (!adminToken) return null;

    const secret = new TextEncoder().encode(
      process.env.NEXTAUTH_SECRET || "fallback-secret"
    );
    const { payload } = await jwtVerify(adminToken, secret);
    return payload;
  } catch {
    return null;
  }
}

// Validation schema for export parameters
const exportQuerySchema = z.object({
  format: z.enum(["csv", "excel"]).default("csv"),
  action: z.string().optional(),
  resource: z.string().optional(),
  adminId: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  search: z.string().optional(),
});

export async function POST(request: NextRequest) {
  try {
    // Check admin authentication
    const adminToken = await verifyAdminToken(request);
    if (!adminToken || adminToken.type !== "admin") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validatedParams = exportQuerySchema.parse(body);

    const { format, action, resource, adminId, startDate, endDate } =
      validatedParams;

    // Resolve audit log service
    const auditLogService =
      container.resolve<AuditLogService>(SERVICE_IDENTIFIERS.AUDIT_LOG_SERVICE);

    // Create admin user entity for permission check
    const adminUserEntity: UserEntity = {
      id: adminToken.id as string,
      email: adminToken.email as string,
      name: adminToken.name as string,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      role: adminToken.role as UserRole,
    };

    // Build export filters
    const exportFilters = {
      action: action || undefined,
      resource: resource || undefined,
      userId: adminId || undefined,
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      format: format as "csv" | "json",
    };

    // Export audit logs using service
    const exportResult = await auditLogService.exportAuditLogs(
      exportFilters,
      adminUserEntity
    );

    // Return the export result from service
    if (format === "csv") {
      return new NextResponse(exportResult, {
        headers: {
          "Content-Type": "text/csv",
          "Content-Disposition": `attachment; filename="audit-logs-${
            new Date().toISOString().split("T")[0]
          }.csv"`,
        },
      });
    } else {
      // For JSON/Excel format, return the data
      return NextResponse.json({
        data: exportResult,
        filename: `audit-logs-${new Date().toISOString().split("T")[0]}.xlsx`,
      });
    }
  } catch (error) {
    console.error("Error exporting audit logs:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid export parameters", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
