import { NextRequest, NextResponse } from "next/server";
import { verifyAdminToken } from "@/lib/admin-auth";
import { z } from "zod";
import { container, SERVICE_IDENTIFIERS } from "../../di-container";
import { EventService } from "../../services/event.service";
import { initializeSystem } from "../../initialize";

const createEventSchema = z.object({
  title: z.string().min(1, "Tiêu đề không được để trống"),
  description: z.string().optional(),
  content: z.string().optional(),
  slug: z.string().min(1, "Slug không được để trống"),
  type: z.enum([
    "PROMOTION",
    "LAUNCH",
    "WORKSHOP",
    "WEBINAR",
    "SALE",
    "SEASONAL",
    "COMMUNITY",
    "OTHER",
  ]),
  status: z
    .enum([
      "DRAFT",
      "PUBLISHED",
      "SCHEDULED",
      "ONGOING",
      "COMPLETED",
      "CANCELLED",
    ])
    .default("DRAFT"),
  startDate: z.string().transform((str) => new Date(str)),
  endDate: z
    .string()
    .transform((str) => new Date(str))
    .optional(),
  isAllDay: z.boolean().default(false),
  location: z.string().optional(),
  maxAttendees: z.number().positive().optional(),
  price: z.number().min(0).optional(),
  currency: z.string().default("VND"),
  imageId: z.string().optional().nullable(),
  tags: z.array(z.string()).default([]),
  seoTitle: z.string().optional(),
  seoDescription: z.string().optional(),
});

const updateEventSchema = createEventSchema.partial().omit({ slug: true });

// GET /api/admin/events - List all events
export async function GET(request: NextRequest) {
  try {
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    // Initialize system and get service
    initializeSystem();
    const _eventService = container.resolve<EventService>(SERVICE_IDENTIFIERS.EVENT_SERVICE);

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const _search = searchParams.get("search") || "";
    const _type = searchParams.get("type") || "";
    const _status = searchParams.get("status") || "";
    const _startDate = searchParams.get("startDate");
    const _endDate = searchParams.get("endDate");

    const _skip = (page - 1) * limit;

    // TODO: Build where clause for EventService
    // For now, we'll skip the where clause building

    // TODO: Implement getEvents and getEventCount methods in EventService
    // For now, return placeholder response
    const events: any[] = [];
    const total = 0;

    return NextResponse.json({
      success: true,
      data: events,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching events:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi tải danh sách sự kiện" },
      { status: 500 }
    );
  }
}

// POST /api/admin/events - Create new event
export async function POST(request: NextRequest) {
  try {
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    // Initialize system and get service
    initializeSystem();
    const _eventService = container.resolve<EventService>(SERVICE_IDENTIFIERS.EVENT_SERVICE);

    const body = await request.json();
    const _validatedData = createEventSchema.parse(body);

    // TODO: Implement getEventBySlug method in EventService
    // For now, skip slug check

    // TODO: Implement createEvent method in EventService
    // For now, return placeholder response
    const event = {
      id: "placeholder-event-id",
      title: _validatedData.title,
      slug: _validatedData.slug,
      createdAt: new Date(),
    };

    return NextResponse.json({
      success: true,
      data: event,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error("Error creating event:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi tạo sự kiện" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/events - Bulk update events
export async function PUT(request: NextRequest) {
  try {
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    // Initialize system and get service
    initializeSystem();
    const _eventService = container.resolve<EventService>(SERVICE_IDENTIFIERS.EVENT_SERVICE);

    const body = await request.json();
    const { ids, data } = body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { success: false, error: "Danh sách ID không hợp lệ" },
        { status: 400 }
      );
    }

    const _validatedData = updateEventSchema.parse(data);

    // TODO: Implement updateManyEvents method in EventService
    // For now, skip the update

    return NextResponse.json({
      success: true,
      message: `Đã cập nhật ${ids.length} sự kiện`,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error("Error bulk updating events:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi cập nhật sự kiện" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/events - Bulk delete events
export async function DELETE(request: NextRequest) {
  try {
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    // Initialize system and get service
    initializeSystem();
    const _eventService = container.resolve<EventService>(SERVICE_IDENTIFIERS.EVENT_SERVICE);

    const { searchParams } = new URL(request.url);
    const idsParam = searchParams.get("ids");

    if (!idsParam) {
      return NextResponse.json(
        { success: false, error: "Danh sách ID không hợp lệ" },
        { status: 400 }
      );
    }

    const ids = idsParam.split(",");

    // TODO: Implement deleteManyEvents method in EventService
    // For now, skip the deletion

    return NextResponse.json({
      success: true,
      message: `Đã xóa ${ids.length} sự kiện`,
    });
  } catch (error) {
    console.error("Error bulk deleting events:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi xóa sự kiện" },
      { status: 500 }
    );
  }
}
