import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { adminAuthOptions } from "@/lib/admin-auth";
import { container, SERVICE_IDENTIFIERS } from "@/app/api/di-container";
import type { SettingService } from "@/app/api/services/setting.service";
import { SettingType } from "@/app/models/setting.model";

// GET /api/admin/settings/[category] - Lấy settings theo category
export async function GET(
  request: NextRequest,
  { params }: { params: { category: string } }
) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    const { category } = params;

    // Define which settings belong to each category
    const categoryKeys: Record<string, string[]> = {
      general: ["siteName", "siteDescription", "siteUrl", "logo", "favicon"],
      contact: [
        "contactEmail",
        "contactPhone",
        "address",
        "district",
        "city",
        "province",
        "postalCode",
        "country",
        "googleMapsUrl",
        "googleMapsEmbed",
        "latitude",
        "longitude",
        "businessHours",
        "whatsappNumber",
        "telegramUsername",
        "skypeId",
        "socialMedia",
      ],
      payment: ["paymentMethods"],
      shipping: ["shippingSettings"],
      email: ["emailSettings"],
      notifications: ["notifications"],
      seo: ["seoSettings"],
      security: ["securitySettings"],
    };

    const keys = categoryKeys[category];
    if (!keys) {
      return NextResponse.json(
        { error: "Category không hợp lệ" },
        { status: 400 }
      );
    }

    const settingService = container.resolve<SettingService>(
      SERVICE_IDENTIFIERS.SETTING_SERVICE
    );

    // Get settings for this category using service
    const allSettings = await settingService.getSettings();
    const settings = allSettings.data.filter((setting: any) =>
      keys.includes(setting.key)
    );

    // Convert to key-value object
    const settingsObject: any = {};
    settings.forEach((setting) => {
      settingsObject[setting.key] = setting.value;
    });

    return NextResponse.json(settingsObject);
  } catch (error) {
    console.error("Get category settings error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy cài đặt" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/settings/[category] - Cập nhật settings theo category
export async function PUT(
  request: NextRequest,
  { params }: { params: { category: string } }
) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    const { category } = params;
    const body = await request.json();

    // Define which settings belong to each category
    const categoryKeys: Record<string, string[]> = {
      general: ["siteName", "siteDescription", "siteUrl", "logo", "favicon"],
      contact: [
        "contactEmail",
        "contactPhone",
        "address",
        "district",
        "city",
        "province",
        "postalCode",
        "country",
        "googleMapsUrl",
        "googleMapsEmbed",
        "latitude",
        "longitude",
        "businessHours",
        "whatsappNumber",
        "telegramUsername",
        "skypeId",
        "socialMedia",
      ],
      payment: ["paymentMethods"],
      shipping: ["shippingSettings"],
      email: ["emailSettings"],
      notifications: ["notifications"],
      seo: ["seoSettings"],
      security: ["securitySettings"],
    };

    const allowedKeys = categoryKeys[category];
    if (!allowedKeys) {
      return NextResponse.json(
        { error: "Category không hợp lệ" },
        { status: 400 }
      );
    }

    // Filter only allowed keys for this category
    const filteredSettings: any = {};
    Object.entries(body).forEach(([key, value]) => {
      if (allowedKeys.includes(key)) {
        filteredSettings[key] = value;
      }
    });

    const settingService = container.resolve<SettingService>(
      SERVICE_IDENTIFIERS.SETTING_SERVICE
    );

    // Create admin user entity for audit
    const adminUserEntity = {
      id: session.user.id || "admin",
      email: session.user.email || "<EMAIL>",
      name: session.user.name || "Admin User",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    } as any;

    // Update each setting using service
    const updatePromises = Object.entries(filteredSettings).map(
      async ([key, value]) => {
        try {
          // Try to get existing setting
          const existingSetting = await settingService.getSettingByKey(key);

          if (existingSetting) {
            // Update existing setting
            return await settingService.updateSetting(
              existingSetting.id,
              { value: value as any },
              adminUserEntity
            );
          } else {
            // Create new setting
            return await settingService.createSetting(
              {
                key,
                value: value as any,
                type:
                  typeof value === "object"
                    ? SettingType.JSON
                    : SettingType.STRING,
                category: category,
              },
              adminUserEntity
            );
          }
        } catch (error) {
          console.error(`Error updating setting ${key}:`, error);
          throw error;
        }
      }
    );

    await Promise.all(updatePromises);

    return NextResponse.json({
      success: true,
      message: `Cài đặt ${category} đã được cập nhật thành công`,
    });
  } catch (error) {
    console.error("Update category settings error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi cập nhật cài đặt" },
      { status: 500 }
    );
  }
}
