import { NextRequest, NextResponse } from "next/server";
import { verifyAdminAuth } from "../../../utils/admin-auth";

// GET /api/admin/shipping/methods - Get shipping methods
export async function GET(request: NextRequest) {
  try {
    const authResult = await verifyAdminAuth(request);

    if (!authResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: authResult.error || "Không có quyền truy cập",
        },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const search = searchParams.get("search");
    const zoneId = searchParams.get("zoneId");

    // Mock data for now - TODO: Implement ShippingMethodService
    const mockMethods = [
      {
        id: "1",
        name: "<PERSON><PERSON><PERSON> hàng tiêu chuẩn",
        description: "<PERSON>iao hàng trong 3-5 ngày làm việc",
        type: "STANDARD",
        baseFee: 30000,
        freeShippingMin: 500000,
        estimatedDays: "3-5 ngày",
        maxWeight: 30,
        maxDimensions: { length: 100, width: 100, height: 100 },
        isActive: true,
        sortOrder: 1,
        zoneId: "1",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        zone: {
          id: "1",
          name: "Hà Nội",
        },
      },
      {
        id: "2",
        name: "Giao hàng nhanh",
        description: "Giao hàng trong 1-2 ngày làm việc",
        type: "EXPRESS",
        baseFee: 50000,
        freeShippingMin: 1000000,
        estimatedDays: "1-2 ngày",
        maxWeight: 20,
        maxDimensions: { length: 80, width: 80, height: 80 },
        isActive: true,
        sortOrder: 2,
        zoneId: "1",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        zone: {
          id: "1",
          name: "Hà Nội",
        },
      },
      {
        id: "3",
        name: "Giao hàng trong ngày",
        description: "Giao hàng trong ngày (chỉ nội thành)",
        type: "SAME_DAY",
        baseFee: 80000,
        freeShippingMin: 2000000,
        estimatedDays: "Trong ngày",
        maxWeight: 10,
        maxDimensions: { length: 50, width: 50, height: 50 },
        isActive: true,
        sortOrder: 3,
        zoneId: "1",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        zone: {
          id: "1",
          name: "Hà Nội",
        },
      },
    ];

    // Filter by search and zoneId if provided
    let filteredMethods = mockMethods;
    
    if (search) {
      filteredMethods = filteredMethods.filter((method) =>
        method.name.toLowerCase().includes(search.toLowerCase()) ||
        method.description?.toLowerCase().includes(search.toLowerCase())
      );
    }

    if (zoneId) {
      filteredMethods = filteredMethods.filter((method) =>
        method.zoneId === zoneId
      );
    }

    // Pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedMethods = filteredMethods.slice(startIndex, endIndex);

    return NextResponse.json({
      success: true,
      data: paginatedMethods,
      pagination: {
        page,
        limit,
        total: filteredMethods.length,
        pages: Math.ceil(filteredMethods.length / limit),
      },
    });
  } catch (error) {
    console.error("Get shipping methods error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi lấy danh sách phương thức giao hàng" },
      { status: 500 }
    );
  }
}

// POST /api/admin/shipping/methods - Create shipping method
export async function POST(request: NextRequest) {
  try {
    const authResult = await verifyAdminAuth(request);

    if (!authResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: authResult.error || "Không có quyền truy cập",
        },
        { status: 403 }
      );
    }

    const body = await request.json();

    // TODO: Implement ShippingMethodService
    const newMethod = {
      id: Date.now().toString(),
      ...body,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      zone: {
        id: body.zoneId,
        name: "Zone Name", // TODO: Get from zone service
      },
    };

    return NextResponse.json({
      success: true,
      data: newMethod,
      message: "Phương thức giao hàng đã được tạo thành công",
    });
  } catch (error) {
    console.error("Create shipping method error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi tạo phương thức giao hàng" },
      { status: 500 }
    );
  }
}
