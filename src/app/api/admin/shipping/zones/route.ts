import { NextRequest, NextResponse } from "next/server";
import { verifyAdminAuth } from "../../../utils/admin-auth";

// GET /api/admin/shipping/zones - Get shipping zones
export async function GET(request: NextRequest) {
  try {
    const authResult = await verifyAdminAuth(request);

    if (!authResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: authResult.error || "Không có quyền truy cập",
        },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const search = searchParams.get("search");

    // Mock data for now - TODO: Implement ShippingZoneService
    const mockZones = [
      {
        id: "1",
        name: "<PERSON>à Nội",
        description: "<PERSON>hu vực nội thành Hà Nội",
        type: "CITY",
        isActive: true,
        sortOrder: 1,
        regions: ["Hà Nội"],
        provinces: ["Hà Nội"],
        districts: [],
        wards: [],
        postalCodes: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        methods: [],
      },
      {
        id: "2",
        name: "TP. Hồ Chí Minh",
        description: "Khu vực nội thành TP. HCM",
        type: "CITY",
        isActive: true,
        sortOrder: 2,
        regions: ["TP. Hồ Chí Minh"],
        provinces: ["TP. Hồ Chí Minh"],
        districts: [],
        wards: [],
        postalCodes: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        methods: [],
      },
      {
        id: "3",
        name: "Miền Bắc",
        description: "Các tỉnh miền Bắc (trừ Hà Nội)",
        type: "REGION",
        isActive: true,
        sortOrder: 3,
        regions: ["Miền Bắc"],
        provinces: ["Hải Phòng", "Quảng Ninh", "Thái Bình", "Nam Định"],
        districts: [],
        wards: [],
        postalCodes: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        methods: [],
      },
    ];

    // Filter by search if provided
    let filteredZones = mockZones;
    if (search) {
      filteredZones = mockZones.filter((zone) =>
        zone.name.toLowerCase().includes(search.toLowerCase()) ||
        zone.description?.toLowerCase().includes(search.toLowerCase())
      );
    }

    // Pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedZones = filteredZones.slice(startIndex, endIndex);

    return NextResponse.json({
      success: true,
      data: paginatedZones,
      pagination: {
        page,
        limit,
        total: filteredZones.length,
        pages: Math.ceil(filteredZones.length / limit),
      },
    });
  } catch (error) {
    console.error("Get shipping zones error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi lấy danh sách khu vực giao hàng" },
      { status: 500 }
    );
  }
}

// POST /api/admin/shipping/zones - Create shipping zone
export async function POST(request: NextRequest) {
  try {
    const authResult = await verifyAdminAuth(request);

    if (!authResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: authResult.error || "Không có quyền truy cập",
        },
        { status: 403 }
      );
    }

    const body = await request.json();

    // TODO: Implement ShippingZoneService
    const newZone = {
      id: Date.now().toString(),
      ...body,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      methods: [],
    };

    return NextResponse.json({
      success: true,
      data: newZone,
      message: "Khu vực giao hàng đã được tạo thành công",
    });
  } catch (error) {
    console.error("Create shipping zone error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi tạo khu vực giao hàng" },
      { status: 500 }
    );
  }
}
