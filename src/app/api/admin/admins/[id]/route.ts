import { NextRequest, NextResponse } from "next/server";
import { container, SERVICE_IDENTIFIERS } from "@/app/api/di-container";
import type { AdminUserService } from "@/app/api/services/admin-user.service";
import { z } from "zod";
import { jwtVerify } from "jose";

// Helper function to verify admin JWT token
async function verifyAdminToken(request: NextRequest) {
  try {
    const adminToken = request.cookies.get("admin-session")?.value;
    if (!adminToken) return null;

    const secret = new TextEncoder().encode(
      process.env.NEXTAUTH_SECRET || "fallback-secret"
    );
    const { payload } = await jwtVerify(adminToken, secret);
    return payload;
  } catch {
    return null;
  }
}

const updateAdminSchema = z.object({
  name: z.string().min(2, "Tên phải có ít nhất 2 ký tự").optional(),
  email: z.string().email("Email không hợp lệ").optional(),
  password: z.string().min(6, "Mật khẩu phải có ít nhất 6 ký tự").optional(),
  role: z.enum(["ADMIN", "MODERATOR"]).optional(),
  phone: z.string().optional(),
  department: z.string().optional(),
  isActive: z.boolean().optional(),
  permissions: z.record(z.boolean()).optional(),
});

// GET /api/admin/admins/[id] - Get specific admin user
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const adminToken = await verifyAdminToken(request);

    if (!adminToken || adminToken.type !== "admin") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    // Users can view their own profile, or ADMIN can view any admin
    if (adminToken.id !== params.id && adminToken.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền xem thông tin này" },
        { status: 403 }
      );
    }

    const adminUserService =
      container.resolve<AdminUserService>(SERVICE_IDENTIFIERS.ADMIN_USER_SERVICE);

    // Create admin user entity for request
    const adminUserEntity = {
      id: adminToken.id as string,
      email: adminToken.email as string,
      name: adminToken.name as string,
      role: adminToken.role as string,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    } as any;

    const adminUser = await adminUserService.getAdminUserById(
      params.id,
      adminUserEntity
    );

    return NextResponse.json(adminUser);
  } catch (error) {
    console.error("Get admin user error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy thông tin quản trị viên" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/admins/[id] - Update admin user
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const adminToken = await verifyAdminToken(request);

    if (!adminToken || adminToken.type !== "admin") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const data = updateAdminSchema.parse(body);

    const adminUserService =
      container.resolve<AdminUserService>(SERVICE_IDENTIFIERS.ADMIN_USER_SERVICE);

    // Create admin user entity for request
    const adminUserEntity = {
      id: adminToken.id as string,
      email: adminToken.email as string,
      name: adminToken.name as string,
      role: adminToken.role as string,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    } as any;

    // Permission checks
    const isSelfUpdate = adminToken.id === params.id;
    const isAdmin = adminToken.role === "ADMIN";

    // Users can update their own profile (except role and isActive)
    // Only ADMIN can update other users or change roles/status
    if (!isSelfUpdate && !isAdmin) {
      return NextResponse.json(
        { error: "Không có quyền cập nhật thông tin này" },
        { status: 403 }
      );
    }

    // Restrict what non-admin users can update about themselves
    if (isSelfUpdate && !isAdmin) {
      if (data.role || data.isActive !== undefined) {
        return NextResponse.json(
          {
            error: "Không thể thay đổi vai trò hoặc trạng thái của chính mình",
          },
          { status: 403 }
        );
      }
    }

    // Prepare update data
    const updateData: any = {};

    if (data.name) updateData.name = data.name;
    if (data.email) updateData.email = data.email;
    if (data.permissions !== undefined) {
      updateData.permissions = data.permissions
        ? Object.keys(data.permissions).filter((key) => data.permissions![key])
        : undefined;
    }

    // Only admin can update these fields
    if (isAdmin) {
      if (data.role) updateData.role = data.role as any;
      if (data.isActive !== undefined) updateData.isActive = data.isActive;
    }

    // Add password if provided
    if (data.password) {
      updateData.password = data.password;
    }

    const updatedAdmin = await adminUserService.updateAdminUser(
      params.id,
      updateData,
      adminUserEntity
    );

    return NextResponse.json({
      message: "Cập nhật thông tin thành công",
      adminUser: updatedAdmin,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Dữ liệu không hợp lệ", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Update admin user error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi cập nhật thông tin" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/admins/[id] - Delete admin user
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const adminToken = await verifyAdminToken(request);

    if (
      !adminToken ||
      adminToken.type !== "admin" ||
      adminToken.role !== "ADMIN"
    ) {
      return NextResponse.json(
        { error: "Chỉ Admin mới có quyền xóa tài khoản quản trị viên" },
        { status: 403 }
      );
    }

    // Prevent self-deletion
    if (adminToken.id === params.id) {
      return NextResponse.json(
        { error: "Không thể xóa tài khoản của chính mình" },
        { status: 400 }
      );
    }

    const adminUserService =
      container.resolve<AdminUserService>(SERVICE_IDENTIFIERS.ADMIN_USER_SERVICE);

    // Create admin user entity for request
    const adminUserEntity = {
      id: adminToken.id as string,
      email: adminToken.email as string,
      name: adminToken.name as string,
      role: adminToken.role as string,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    } as any;

    await adminUserService.deleteAdminUser(params.id, adminUserEntity);

    return NextResponse.json({
      message: "Xóa tài khoản quản trị viên thành công",
    });
  } catch (error) {
    console.error("Delete admin user error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi xóa tài khoản" },
      { status: 500 }
    );
  }
}
