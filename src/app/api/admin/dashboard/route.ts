import { NextRequest, NextResponse } from "next/server";
import { container, SERVICE_IDENTIFIERS } from "@/app/api/di-container";
import type { ProductService } from "@/app/api/services/product.service";
import type { OrderService } from "@/app/api/services/order.service";
import type { UserService } from "@/app/api/services/user.service";
import { jwtVerify } from "jose";
import { ProductStatus } from "@/app/models/common.model";

// Helper function to verify admin JWT token
async function verifyAdminToken(request: NextRequest) {
  try {
    const adminToken = request.cookies.get("admin-session")?.value;
    if (!adminToken) return null;

    const secret = new TextEncoder().encode(
      process.env.NEXTAUTH_SECRET || "fallback-secret"
    );
    const { payload } = await jwtVerify(adminToken, secret);
    return payload;
  } catch {
    return null;
  }
}

// GET /api/admin/dashboard - <PERSON><PERSON>y thống kê dashboard
export async function GET(request: NextRequest) {
  try {
    const adminToken = await verifyAdminToken(request);

    if (!adminToken || adminToken.type !== "admin") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    // Both ADMIN and MODERATOR can access dashboard
    if (adminToken.role !== "ADMIN" && adminToken.role !== "MODERATOR") {
      return NextResponse.json(
        { error: "Không có quyền truy cập dashboard" },
        { status: 403 }
      );
    }

    // Resolve services
    const productService = container.resolve<ProductService>(
      SERVICE_IDENTIFIERS.PRODUCT_SERVICE
    );
    const orderService = container.resolve<OrderService>(
      SERVICE_IDENTIFIERS.ORDER_SERVICE
    );
    const userService = container.resolve<UserService>(
      SERVICE_IDENTIFIERS.USER_SERVICE
    );

    // Get current month and last month dates
    const now = new Date();
    const currentMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);

    // Get basic counts using services
    const [totalProducts, totalOrders, totalCustomers] = await Promise.all([
      productService.getProductCount({ status: ProductStatus.ACTIVE }),
      orderService.getOrderCount(),
      userService.getUserCount(),
    ]);

    // Get monthly stats using service
    const [currentMonthRevenue, lastMonthRevenue] = await Promise.all([
      orderService.getRevenueByDateRange(currentMonthStart, new Date()),
      orderService.getRevenueByDateRange(lastMonthStart, lastMonthEnd),
    ]);

    // Get recent orders using service
    const recentOrders = await orderService.getRecentOrders(5);

    // Get top products using service
    const topProducts = await productService.getTopSellingProducts(5);

    // Calculate total revenue using service
    const totalRevenue = await orderService.getTotalRevenue();

    const dashboardStats = {
      totalProducts,
      totalOrders,
      totalCustomers,
      totalRevenue,
      recentOrders,
      topProducts,
      monthlyStats: {
        currentMonth: {
          revenue: currentMonthRevenue,
        },
        lastMonth: {
          revenue: lastMonthRevenue,
        },
      },
    };

    return NextResponse.json(dashboardStats);
  } catch (error) {
    console.error("Get dashboard stats error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy thống kê dashboard" },
      { status: 500 }
    );
  }
}
