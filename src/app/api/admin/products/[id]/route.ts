import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { jwtVerify } from "jose";
import { container, SERVICE_IDENTIFIERS } from "@/app/api/di-container";
import { NotFoundError } from "@/app/api/repositories/base.repository";
import { ProductService } from "@/app/api/services/product.service";
import { ProductStatus } from "@/app/models/common.model";

// Helper function to verify admin authentication
async function verifyAdminAuth(request: NextRequest) {
  try {
    const adminToken = request.cookies.get("admin-session")?.value;

    if (!adminToken) {
      return null;
    }

    const secret = new TextEncoder().encode(
      process.env.NEXTAUTH_SECRET || "fallback-secret"
    );

    const { payload } = await jwtVerify(adminToken, secret);

    if (payload.role !== "ADMIN" && payload.role !== "MODERATOR") {
      return null;
    }

    return {
      id: payload.id as string,
      role: payload.role as string,
      type: "admin" as const,
    };
  } catch {
    return null;
  }
}

const updateProductSchema = z.object({
  name: z.string().min(1).optional(),
  description: z.string().min(1).optional(),
  price: z.number().positive().optional(),
  salePrice: z.number().positive().optional(),
  images: z.array(z.string()).optional(),
  imageTypes: z.array(z.enum(["INTERNAL", "EXTERNAL"])).optional(),
  externalImages: z.array(z.string()).optional(),
  categoryId: z.string().optional(),
  brandId: z.string().optional(),

  stock: z.number().int().min(0).optional(),
  sku: z.string().optional(),
  slug: z.string().optional(),
  featured: z.boolean().optional(),
  status: z.enum(["ACTIVE", "INACTIVE", "OUT_OF_STOCK"]).optional(),
  tags: z.array(z.string()).optional(),
  attributes: z
    .array(
      z.object({
        attributeId: z.string(),
        attributeValueId: z.string(),
      })
    )
    .optional(),
});

// GET /api/admin/products/[id] - Lấy chi tiết sản phẩm
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const adminUser = await verifyAdminAuth(request);

    if (!adminUser) {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    // Await params before accessing properties (Next.js 15 requirement)
    const { id } = await params;

    // Get ProductService from DI container
    const productService = container.resolve<ProductService>(
      SERVICE_IDENTIFIERS.PRODUCT_SERVICE
    );

    // Get product by ID with relations
    const product = await productService.getProductById(id);

    if (!product) {
      return NextResponse.json(
        { error: "Không tìm thấy sản phẩm" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: product,
    });
  } catch (error) {
    if (error instanceof NotFoundError) {
      return NextResponse.json(
        { error: "Không tìm thấy sản phẩm" },
        { status: 404 }
      );
    }

    console.error("Get product error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy thông tin sản phẩm" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/products/[id] - Cập nhật sản phẩm
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const adminUser = await verifyAdminAuth(request);

    if (!adminUser) {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    // Await params before accessing properties (Next.js 15 requirement)
    const { id } = await params;

    const body = await request.json();

    // Enhanced validation
    const validationErrors: string[] = [];

    if (body.name !== undefined) {
      if (!body.name || body.name.trim().length === 0) {
        validationErrors.push("Tên sản phẩm không được để trống");
      } else if (body.name.length < 3) {
        validationErrors.push("Tên sản phẩm phải có ít nhất 3 ký tự");
      } else if (body.name.length > 255) {
        validationErrors.push("Tên sản phẩm không được vượt quá 255 ký tự");
      }
    }

    if (body.description !== undefined) {
      if (!body.description || body.description.trim().length === 0) {
        validationErrors.push("Mô tả sản phẩm không được để trống");
      } else if (body.description.length < 10) {
        validationErrors.push("Mô tả sản phẩm phải có ít nhất 10 ký tự");
      }
    }

    if (body.price !== undefined) {
      if (!body.price || parseFloat(body.price) <= 0) {
        validationErrors.push("Giá sản phẩm phải lớn hơn 0");
      } else if (parseFloat(body.price) > 999999999) {
        validationErrors.push(
          "Giá sản phẩm không được vượt quá 999,999,999 VND"
        );
      }
    }

    if (body.salePrice !== undefined && body.salePrice !== null) {
      if (body.price && parseFloat(body.salePrice) >= parseFloat(body.price)) {
        validationErrors.push("Giá khuyến mãi phải nhỏ hơn giá gốc");
      }
    }

    if (body.stock !== undefined && parseInt(body.stock) < 0) {
      validationErrors.push("Số lượng tồn kho không được âm");
    }

    if (validationErrors.length > 0) {
      return NextResponse.json(
        { error: "Dữ liệu không hợp lệ", details: validationErrors },
        { status: 400 }
      );
    }

    const data = updateProductSchema.parse(body);
    const productService = container.resolve<ProductService>(
      SERVICE_IDENTIFIERS.PRODUCT_SERVICE
    );

    // Create admin user entity for audit
    const adminUserEntity = {
      id: adminUser.id,
      email: "<EMAIL>",
      name: "Admin User",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    } as any;

    // Transform data to match UpdateProductData interface
    const updateData = {
      ...data,
      status: data.status ? (data.status as ProductStatus) : undefined,
    };

    // Update product using service
    const updatedProduct = await productService.updateProduct(
      id,
      updateData,
      adminUserEntity
    );

    return NextResponse.json({
      success: true,
      data: updatedProduct,
      message: "Sản phẩm đã được cập nhật thành công",
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error("Update product error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi cập nhật sản phẩm" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/products/[id] - Xóa sản phẩm
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const adminUser = await verifyAdminAuth(request);

    if (!adminUser) {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    // Await params before accessing properties (Next.js 15 requirement)
    const { id } = await params;

    // Get ProductService from DI container
    const productService = container.resolve<ProductService>(
      SERVICE_IDENTIFIERS.PRODUCT_SERVICE
    );

    // Delete product using service
    // Create a mock UserEntity for the admin user
    const adminUserEntity = {
      ...adminUser,
      email: "<EMAIL>",
      name: "Admin User",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    } as any;

    await productService.deleteProduct(id, adminUserEntity);

    return NextResponse.json({
      success: true,
      message: "Sản phẩm đã được xóa thành công",
    });
  } catch (error) {
    if (error instanceof NotFoundError) {
      return NextResponse.json(
        { error: "Không tìm thấy sản phẩm" },
        { status: 404 }
      );
    }

    console.error("Delete product error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi xóa sản phẩm" },
      { status: 500 }
    );
  }
}
