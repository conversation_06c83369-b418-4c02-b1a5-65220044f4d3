import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { adminAuthOptions } from "@/lib/admin-auth";
import { z } from "zod";
import { container, SERVICE_IDENTIFIERS } from "../../../di-container";
import type { ProductService } from "../../../services/product.service";
import type { CategoryService } from "../../../services/category.service";
import { UserRole, ProductStatus } from "@/app/models/common.model";

const bulkUpdateSchema = z.object({
  productIds: z.array(z.string()).min(1, "Phải chọn ít nhất 1 sản phẩm"),
  action: z.enum([
    "delete",
    "activate",
    "deactivate",
    "feature",
    "unfeature",
    "update_category",
  ]),
  data: z
    .object({
      categoryId: z.string().optional(),
    })
    .optional(),
});

// POST /api/admin/products/bulk - Bulk operations cho products
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { productIds, action, data } = bulkUpdateSchema.parse(body);

    // Resolve services
    const productService = container.resolve<ProductService>(
      SERVICE_IDENTIFIERS.PRODUCT_SERVICE
    );
    const categoryService = container.resolve<CategoryService>(
      SERVICE_IDENTIFIERS.CATEGORY_SERVICE
    );

    // Create admin user entity
    const adminUserEntity = {
      id: session.user.id,
      email: session.user.email || "",
      name: session.user.name || "",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      role: session.user.role as UserRole,
    };

    let result;
    let message = "";

    switch (action) {
      case "delete":
        // Bulk soft delete using service
        result = await productService.bulkUpdateProducts(
          productIds,
          { status: ProductStatus.INACTIVE },
          adminUserEntity
        );
        message = `Đã xóa ${result.count} sản phẩm`;
        break;

      case "activate":
        result = await productService.bulkUpdateProducts(
          productIds,
          { status: ProductStatus.ACTIVE },
          adminUserEntity
        );
        message = `Đã kích hoạt ${result.count} sản phẩm`;
        break;

      case "deactivate":
        result = await productService.bulkUpdateProducts(
          productIds,
          { status: ProductStatus.INACTIVE },
          adminUserEntity
        );
        message = `Đã vô hiệu hóa ${result.count} sản phẩm`;
        break;

      case "feature":
        result = await productService.bulkUpdateProducts(
          productIds,
          { featured: true },
          adminUserEntity
        );
        message = `Đã đặt ${result.count} sản phẩm làm nổi bật`;
        break;

      case "unfeature":
        result = await productService.bulkUpdateProducts(
          productIds,
          { featured: false },
          adminUserEntity
        );
        message = `Đã bỏ nổi bật ${result.count} sản phẩm`;
        break;

      case "update_category": {
        if (!data?.categoryId) {
          return NextResponse.json(
            { error: "Phải chọn danh mục" },
            { status: 400 }
          );
        }

        // Check if category exists using service
        try {
          await categoryService.getCategoryById(data.categoryId);
        } catch (_error) {
          return NextResponse.json(
            { error: "Danh mục không tồn tại" },
            { status: 400 }
          );
        }

        result = await productService.bulkUpdateProducts(
          productIds,
          { categoryId: data.categoryId },
          adminUserEntity
        );
        message = `Đã cập nhật danh mục cho ${result.count} sản phẩm`;
        break;
      }

      default:
        return NextResponse.json(
          { error: "Hành động không hợp lệ" },
          { status: 400 }
        );
    }

    return NextResponse.json({
      message,
      count: result.count,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error("Bulk products operation error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi thực hiện thao tác" },
      { status: 500 }
    );
  }
}
