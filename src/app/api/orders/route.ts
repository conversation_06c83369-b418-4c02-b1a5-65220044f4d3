import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../auth/[...nextauth]/route";
import { z } from "zod";
import { emailService } from "@/lib/email-service";
import type { OrderConfirmationEmailData } from "@/lib/email/templates";
import { container, SERVICE_IDENTIFIERS } from "../di-container";
import type { OrderService } from "../services/order.service";
import type { UserService } from "../services/user.service";
import { PaymentMethod, ShippingMethod } from "../../models/order.model";

// Type for shipping address JSON field
interface ShippingAddress {
  fullName: string;
  address: string;
  ward: string;
  district: string;
  province: string;
  phone: string;
}

const createOrderSchema = z.object({
  items: z
    .array(
      z.object({
        productId: z.string(),
        quantity: z.number().min(1),
        price: z.number().min(0),
      })
    )
    .min(1, "Đơn hàng phải có ít nhất 1 sản phẩm"),
  shippingAddress: z.object({
    fullName: z.string().min(1, "Họ tên là bắt buộc"),
    phone: z.string().min(1, "Số điện thoại là bắt buộc"),
    address: z.string().min(1, "Địa chỉ là bắt buộc"),
    ward: z.string().min(1, "Phường/Xã là bắt buộc"),
    district: z.string().min(1, "Quận/Huyện là bắt buộc"),
    province: z.string().min(1, "Tỉnh/Thành phố là bắt buộc"),
  }),
  billingAddress: z
    .object({
      fullName: z.string().min(1, "Họ tên là bắt buộc"),
      phone: z.string().min(1, "Số điện thoại là bắt buộc"),
      address: z.string().min(1, "Địa chỉ là bắt buộc"),
      ward: z.string().min(1, "Phường/Xã là bắt buộc"),
      district: z.string().min(1, "Quận/Huyện là bắt buộc"),
      province: z.string().min(1, "Tỉnh/Thành phố là bắt buộc"),
    })
    .optional(),
  paymentMethod: z.enum(["COD", "BANK_TRANSFER", "CREDIT_CARD"]),
  shippingMethod: z.enum(["STANDARD", "EXPRESS", "OVERNIGHT"]).optional(),
  notes: z.string().optional(),
});

// GET /api/orders - Lấy danh sách đơn hàng của user
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Vui lòng đăng nhập" },
        { status: 401 }
      );
    }

    const orderService = container.resolve<OrderService>(SERVICE_IDENTIFIERS.ORDER_SERVICE);

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const status = searchParams.get("status");

    // Build filters
    const filters: any = {
      userId: session.user.id,
      page,
      limit,
    };

    if (status) {
      filters.status = status;
    }

    // Get orders with pagination using service
    const result = await orderService.searchOrders(filters);

    return NextResponse.json({
      orders: result.data,
      pagination: {
        page,
        limit,
        total: result.total,
        pages: Math.ceil(result.total / limit),
      },
    });
  } catch (error) {
    console.error("Get orders error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy danh sách đơn hàng" },
      { status: 500 }
    );
  }
}

// POST /api/orders - Tạo đơn hàng mới
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Vui lòng đăng nhập" },
        { status: 401 }
      );
    }

    const orderService = container.resolve<OrderService>(SERVICE_IDENTIFIERS.ORDER_SERVICE);
    const userService = container.resolve<UserService>(SERVICE_IDENTIFIERS.USER_SERVICE);

    const body = await request.json();
    const data = createOrderSchema.parse(body);

    // Create user entity
    const userEntity = {
      id: session.user.id,
      email: session.user.email || "",
      name: session.user.name || "",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    } as any;

    // Transform address data to match AddressData interface
    const transformAddress = (addr: any) => ({
      street: addr.address || "",
      city: addr.district || "",
      state: addr.province || "",
      country: "Vietnam",
      postalCode: addr.postalCode || "",
    });

    // Transform payment method
    const transformPaymentMethod = (method: string) => {
      switch (method) {
        case "COD":
          return PaymentMethod.CASH_ON_DELIVERY;
        default:
          return method as PaymentMethod;
      }
    };

    // Create order using service
    const orderData = {
      userId: session.user.id,
      items: data.items,
      shippingMethod: (data.shippingMethod ||
        ShippingMethod.STANDARD) as ShippingMethod,
      shippingAddress: transformAddress(data.shippingAddress),
      billingAddress: data.billingAddress
        ? transformAddress(data.billingAddress)
        : undefined,
      paymentMethod: transformPaymentMethod(data.paymentMethod),
      notes: data.notes,
    };
    const order = await orderService.createOrder(orderData, userEntity);

    // Send order confirmation email
    try {
      await emailService.initialize();

      // Get user details using service
      const user = await userService.getUserById(session.user.id);

      if (user) {
        const orderConfirmationData: OrderConfirmationEmailData = {
          recipientName: user.name,
          recipientEmail: user.email,
          order: {
            id: order.id,
            total: order.total,
            status: order.status,
            createdAt: order.createdAt.toISOString(),
            items: order.items.map((item) => ({
              name: item.productName,
              quantity: item.quantity,
              price: item.unitPrice,
              image: undefined, // TODO: Get product image from service if needed
            })),
            shippingAddress: order.shippingAddress
              ? (() => {
                  const addr =
                    order.shippingAddress as unknown as ShippingAddress;
                  return {
                    fullName: addr.fullName,
                    address: addr.address,
                    city: `${addr.ward}, ${addr.district}, ${addr.province}`,
                    postalCode: "", // Add postal code if available in your schema
                    phone: addr.phone,
                  };
                })()
              : {
                  fullName: "N/A",
                  address: "N/A",
                  city: "N/A",
                  postalCode: "",
                  phone: "N/A",
                },
          },
          trackingUrl: `${process.env.NEXT_PUBLIC_APP_URL}/orders/${order.id}`,
        };

        await emailService.sendOrderConfirmationEmail(orderConfirmationData);
        console.log(
          `Order confirmation email sent to ${user.email} for order ${order.id}`
        );
      }
    } catch (error) {
      console.error("Failed to send order confirmation email:", error);
      // Don't fail order creation if email fails
    }

    return NextResponse.json(
      {
        message: "Đặt hàng thành công",
        order,
      },
      { status: 201 }
    );
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error("Create order error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi tạo đơn hàng" },
      { status: 500 }
    );
  }
}
