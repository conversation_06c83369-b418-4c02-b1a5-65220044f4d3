"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Mail,
  Phone,
  Building,
  Calendar,
  Search,
  Filter,
  Eye,
  MessageSquare,
  User,
  Clock,
  AlertCircle,
  CheckCircle,
  XCircle,
  Loader2,
} from "lucide-react";
import { toast } from "sonner";
import { EmailTestDialog } from "@/components/admin/email-test-dialog";
import {
  useContacts,
  type Contact as ContactType,
  type ContactFilters,
} from "@/lib/admin/hooks";

// Using Contact type from hooks
type Contact = ContactType;

const statusColors = {
  pending: "bg-blue-100 text-blue-800",
  in_progress: "bg-purple-100 text-purple-800",
  resolved: "bg-green-100 text-green-800",
  rejected: "bg-red-100 text-red-800",
};

const priorityColors = {
  low: "bg-gray-100 text-gray-800",
  medium: "bg-blue-100 text-blue-800",
  high: "bg-orange-100 text-orange-800",
  urgent: "bg-red-100 text-red-800",
};

export default function ContactsPage() {
  const {
    contacts,
    stats,
    loading,
    fetchContacts,
    fetchStats,
    updateContactStatus,
  } = useContacts();
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [filters, setFilters] = useState<ContactFilters>({
    search: "",
    status: "all",
    priority: "all",
    page: 1,
    limit: 10,
  });

  useEffect(() => {
    const loadData = async () => {
      await Promise.all([fetchContacts(filters), fetchStats()]);
    };

    loadData();
  }, [filters, fetchContacts, fetchStats]);

  const handleStatusUpdate = async (
    contactId: string,
    status: Contact["status"]
  ) => {
    await updateContactStatus(contactId, status);
    // Refresh contacts after successful update
    await fetchContacts(filters);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("vi-VN", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Quản lý liên hệ</h1>
          <p className="text-muted-foreground">
            Quản lý và phản hồi các tin nhắn liên hệ từ khách hàng
          </p>
        </div>
        <EmailTestDialog />
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <MessageSquare className="h-4 w-4 text-blue-600" />
                <div>
                  <p className="text-sm text-muted-foreground">Tổng số</p>
                  <p className="text-2xl font-bold">{stats.total}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4 text-blue-600" />
                <div>
                  <p className="text-sm text-muted-foreground">Chờ xử lý</p>
                  <p className="text-2xl font-bold">{stats.pending}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-yellow-600" />
                <div>
                  <p className="text-sm text-muted-foreground">Đang xử lý</p>
                  <p className="text-2xl font-bold">{stats.inProgress}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <div>
                  <p className="text-sm text-muted-foreground">Đã giải quyết</p>
                  <p className="text-2xl font-bold">{stats.resolved}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Tìm kiếm theo tên, email, công ty..."
                  value={filters.search}
                  onChange={(e) =>
                    setFilters({ ...filters, search: e.target.value, page: 1 })
                  }
                  className="pl-10"
                />
              </div>
            </div>

            <Select
              value={filters.status}
              onValueChange={(value) =>
                setFilters({ ...filters, status: value, page: 1 })
              }
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Trạng thái" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả trạng thái</SelectItem>
                <SelectItem value="pending">Chờ xử lý</SelectItem>
                <SelectItem value="in_progress">Đang xử lý</SelectItem>
                <SelectItem value="resolved">Đã giải quyết</SelectItem>
                <SelectItem value="rejected">Từ chối</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={filters.priority}
              onValueChange={(value) =>
                setFilters({ ...filters, priority: value, page: 1 })
              }
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Độ ưu tiên" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả độ ưu tiên</SelectItem>
                <SelectItem value="low">Thấp</SelectItem>
                <SelectItem value="medium">Bình thường</SelectItem>
                <SelectItem value="high">Cao</SelectItem>
                <SelectItem value="urgent">Khẩn cấp</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Contacts Table */}
      <Card>
        <CardHeader>
          <CardTitle>Danh sách liên hệ</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Khách hàng</TableHead>
                <TableHead>Liên hệ</TableHead>
                <TableHead>Dịch vụ</TableHead>
                <TableHead>Trạng thái</TableHead>
                <TableHead>Độ ưu tiên</TableHead>
                <TableHead>Ngày tạo</TableHead>
                <TableHead>Thao tác</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {contacts.map((contact) => (
                <TableRow key={contact.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{contact.name}</div>
                      {contact.company && (
                        <div className="text-sm text-muted-foreground flex items-center gap-1">
                          <Building className="h-3 w-3" />
                          {contact.company}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex items-center gap-1 text-sm">
                        <Mail className="h-3 w-3" />
                        {contact.email}
                      </div>
                      {contact.phone && (
                        <div className="flex items-center gap-1 text-sm text-muted-foreground">
                          <Phone className="h-3 w-3" />
                          {contact.phone}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      {contact.service && (
                        <div className="text-sm">{contact.service}</div>
                      )}
                      {contact.subject && (
                        <div className="text-sm text-muted-foreground">
                          {contact.subject}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Select
                      value={contact.status}
                      onValueChange={(value) =>
                        handleStatusUpdate(
                          contact.id,
                          value as Contact["status"]
                        )
                      }
                    >
                      <SelectTrigger className="w-[140px]">
                        <Badge className={statusColors[contact.status]}>
                          {contact.status === "pending"
                            ? "Chờ xử lý"
                            : contact.status === "in_progress"
                              ? "Đang xử lý"
                              : contact.status === "resolved"
                                ? "Đã giải quyết"
                                : "Từ chối"}
                        </Badge>
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="pending">Chờ xử lý</SelectItem>
                        <SelectItem value="in_progress">Đang xử lý</SelectItem>
                        <SelectItem value="resolved">Đã giải quyết</SelectItem>
                        <SelectItem value="rejected">Từ chối</SelectItem>
                      </SelectContent>
                    </Select>
                  </TableCell>
                  <TableCell>
                    <Badge className={priorityColors[contact.priority]}>
                      {contact.priority}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1 text-sm">
                      <Calendar className="h-3 w-3" />
                      {formatDate(contact.createdAt)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedContact(contact)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                        <DialogHeader>
                          <DialogTitle>Chi tiết liên hệ</DialogTitle>
                        </DialogHeader>
                        {selectedContact && (
                          <div className="space-y-4">
                            {/* Contact Info */}
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <label className="text-sm font-medium">
                                  Họ tên
                                </label>
                                <p className="text-sm">
                                  {selectedContact.name}
                                </p>
                              </div>
                              <div>
                                <label className="text-sm font-medium">
                                  Email
                                </label>
                                <p className="text-sm">
                                  {selectedContact.email}
                                </p>
                              </div>
                              {selectedContact.phone && (
                                <div>
                                  <label className="text-sm font-medium">
                                    Điện thoại
                                  </label>
                                  <p className="text-sm">
                                    {selectedContact.phone}
                                  </p>
                                </div>
                              )}
                              {selectedContact.company && (
                                <div>
                                  <label className="text-sm font-medium">
                                    Công ty
                                  </label>
                                  <p className="text-sm">
                                    {selectedContact.company}
                                  </p>
                                </div>
                              )}
                            </div>

                            {/* Service & Subject */}
                            {selectedContact.service && (
                              <div>
                                <label className="text-sm font-medium">
                                  Dịch vụ quan tâm
                                </label>
                                <p className="text-sm">
                                  {selectedContact.service}
                                </p>
                              </div>
                            )}
                            {selectedContact.subject && (
                              <div>
                                <label className="text-sm font-medium">
                                  Chủ đề
                                </label>
                                <p className="text-sm">
                                  {selectedContact.subject}
                                </p>
                              </div>
                            )}

                            {/* Message */}
                            <div>
                              <label className="text-sm font-medium">
                                Tin nhắn
                              </label>
                              <div className="mt-1 p-3 bg-gray-50 rounded-lg">
                                <p className="text-sm whitespace-pre-wrap">
                                  {selectedContact.message}
                                </p>
                              </div>
                            </div>

                            {/* Status & Priority */}
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <label className="text-sm font-medium">
                                  Trạng thái
                                </label>
                                <div className="mt-1">
                                  <Badge
                                    className={
                                      statusColors[selectedContact.status]
                                    }
                                  >
                                    {selectedContact.status}
                                  </Badge>
                                </div>
                              </div>
                              <div>
                                <label className="text-sm font-medium">
                                  Độ ưu tiên
                                </label>
                                <div className="mt-1">
                                  <Badge
                                    className={
                                      priorityColors[selectedContact.priority]
                                    }
                                  >
                                    {selectedContact.priority}
                                  </Badge>
                                </div>
                              </div>
                            </div>

                            {/* Timestamps */}
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <label className="text-sm font-medium">
                                  Ngày tạo
                                </label>
                                <p className="text-sm">
                                  {formatDate(selectedContact.createdAt)}
                                </p>
                              </div>
                              <div>
                                <label className="text-sm font-medium">
                                  Cập nhật cuối
                                </label>
                                <p className="text-sm">
                                  {formatDate(selectedContact.updatedAt)}
                                </p>
                              </div>
                            </div>

                            {/* Notes */}
                            {selectedContact.notes.length > 0 && (
                              <div>
                                <label className="text-sm font-medium">
                                  Ghi chú
                                </label>
                                <div className="mt-2 space-y-2">
                                  {selectedContact.notes.map((note) => (
                                    <div
                                      key={note.id}
                                      className="p-3 bg-gray-50 rounded-lg"
                                    >
                                      <div className="flex items-center justify-between mb-1">
                                        <span className="text-xs font-medium">
                                          {note.admin.name}
                                        </span>
                                        <span className="text-xs text-muted-foreground">
                                          {formatDate(note.createdAt)}
                                        </span>
                                      </div>
                                      <p className="text-sm">{note.note}</p>
                                      {note.isInternal && (
                                        <Badge
                                          variant="secondary"
                                          className="mt-1 text-xs"
                                        >
                                          Nội bộ
                                        </Badge>
                                      )}
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        )}
                      </DialogContent>
                    </Dialog>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {contacts.length === 0 && (
            <div className="text-center py-8">
              <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">Không có liên hệ nào</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
