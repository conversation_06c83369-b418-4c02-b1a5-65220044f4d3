"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Tag,
  ChevronRight,
  Package,
} from "lucide-react";
import { toast } from "sonner";
import { useAdminCategories } from "@/hooks/admin";

interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  parentId?: string;
  createdAt: string;
  children?: Category[];
  _count: {
    products: number;
    children: number;
  };
}

export default function AdminCategoriesPage() {
  // Using admin categories hook
  const { categories, loading, fetchCategories, deleteCategory } =
    useAdminCategories();
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    fetchCategories();
  }, []);

  // Removed local fetchCategories - using hook method

  const handleDeleteCategory = async (
    categoryId: string,
    categoryName: string
  ) => {
    if (!confirm(`Bạn có chắc chắn muốn xóa danh mục "${categoryName}"?`)) {
      return;
    }

    try {
      const success = await deleteCategory(categoryId);
      if (success) {
        toast.success("Xóa danh mục thành công");
      }
    } catch (error) {
      toast.error("Có lỗi xảy ra khi xóa danh mục");
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("vi-VN");
  };

  const filteredCategories = categories.filter(
    (category) =>
      category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      category.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const parentCategories = filteredCategories.filter((cat) => !cat.parentId);

  const renderCategoryTree = (category: Category, level = 0) => {
    const children = filteredCategories.filter(
      (cat) => cat.parentId === category.id
    );

    return (
      <div key={category.id}>
        <div
          className={`flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors ${
            level > 0 ? "ml-8 border-l-4 border-l-pink-200" : ""
          }`}
        >
          <div className="flex items-center gap-3">
            <div
              className={`flex items-center gap-2 ${level > 0 ? "text-gray-600" : ""}`}
            >
              {level > 0 && <ChevronRight className="h-4 w-4" />}
              <Tag className="h-5 w-5 text-pink-600" />
              <div>
                <h3 className="font-medium">{category.name}</h3>
                {category.description && (
                  <p className="text-sm text-muted-foreground">
                    {category.description}
                  </p>
                )}
                <div className="flex items-center gap-4 text-xs text-muted-foreground mt-1">
                  <span className="flex items-center gap-1">
                    <Package className="h-3 w-3" />
                    {category._count?.products || 0} sản phẩm
                  </span>
                  {(category._count?.children || 0) > 0 && (
                    <span className="flex items-center gap-1">
                      <Tag className="h-3 w-3" />
                      {category._count?.children || 0} danh mục con
                    </span>
                  )}
                  <span>Tạo: {formatDate(category.createdAt)}</span>
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Link href={`/admin/categories/${category.id}/edit`}>
              <Button variant="ghost" size="sm">
                <Edit className="h-4 w-4" />
              </Button>
            </Link>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleDeleteCategory(category.id, category.name)}
              className="text-red-600 hover:text-red-700 hover:bg-red-50"
              disabled={
                category._count.products > 0 || category._count.children > 0
              }
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Render children */}
        {children.map((child) => renderCategoryTree(child, level + 1))}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Quản lý danh mục</h1>
          <p className="text-muted-foreground">
            Quản lý danh mục sản phẩm và cấu trúc phân cấp
          </p>
        </div>
        <Link href="/admin/categories/create">
          <Button className="bg-pink-600 hover:bg-pink-700">
            <Plus className="h-4 w-4 mr-2" />
            Thêm danh mục
          </Button>
        </Link>
      </div>

      {/* Search */}
      <Card>
        <CardHeader>
          <CardTitle>Tìm kiếm danh mục</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <input
              type="text"
              placeholder="Tìm kiếm theo tên hoặc mô tả..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
            />
          </div>
        </CardContent>
      </Card>

      {/* Categories List */}
      <Card>
        <CardHeader>
          <CardTitle>Danh sách danh mục ({categories.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {Array.from({ length: 5 }, (_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-5 h-5 bg-gray-200 rounded" />
                      <div className="space-y-2">
                        <div className="h-4 bg-gray-200 rounded w-32" />
                        <div className="h-3 bg-gray-200 rounded w-48" />
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <div className="w-8 h-8 bg-gray-200 rounded" />
                      <div className="w-8 h-8 bg-gray-200 rounded" />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : parentCategories.length === 0 ? (
            <div className="text-center py-12">
              <Tag className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                Chưa có danh mục nào
              </h3>
              <p className="text-muted-foreground mb-4">
                Bắt đầu bằng cách tạo danh mục đầu tiên
              </p>
              <Link href="/admin/categories/create">
                <Button className="bg-pink-600 hover:bg-pink-700">
                  <Plus className="h-4 w-4 mr-2" />
                  Tạo danh mục đầu tiên
                </Button>
              </Link>
            </div>
          ) : (
            <div className="space-y-4">
              {parentCategories.map((category) => renderCategoryTree(category))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Summary */}
      {!loading && categories.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Tag className="h-5 w-5 text-pink-600" />
                <div>
                  <p className="text-sm text-muted-foreground">Tổng danh mục</p>
                  <p className="text-2xl font-bold">{categories.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Tag className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="text-sm text-muted-foreground">Danh mục cha</p>
                  <p className="text-2xl font-bold">
                    {parentCategories.length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Package className="h-5 w-5 text-green-600" />
                <div>
                  <p className="text-sm text-muted-foreground">Tổng sản phẩm</p>
                  <p className="text-2xl font-bold">
                    {categories.reduce(
                      (sum, cat) => sum + cat._count.products,
                      0
                    )}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
