"use client";

import { useEffect, useState } from "react";
import { ShoppingBag, Users, Star, Truck } from "lucide-react";
import { useSettings } from "@/contexts";
import { useFormattedShopStats } from "@/hooks/use-shop-stats";

interface StatItem {
  icon: React.ReactNode;
  value: string;
  label: string;
  suffix?: string;
}

export function StatsSection() {
  const { settings } = useSettings();
  const { stats: shopStats, loading, error } = useFormattedShopStats();
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Set visible to true after a short delay to ensure stats show up
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 500);

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    const element = document.getElementById("stats-section");
    if (element) {
      observer.observe(element);
    }

    return () => {
      clearTimeout(timer);
      observer.disconnect();
    };
  }, []);

  // Add icons to the stats from hook
  const stats: StatItem[] = shopStats.map((stat, index) => {
    const icons = [
      <ShoppingBag className="h-8 w-8 text-fashion-500" />,
      <Users className="h-8 w-8 text-blue-500" />,
      <Star className="h-8 w-8 text-yellow-500" />,
      <Truck className="h-8 w-8 text-green-500" />,
    ];

    return {
      ...stat,
      icon: icons[index] || (
        <ShoppingBag className="h-8 w-8 text-fashion-500" />
      ),
    };
  });

  // Override delivery time with settings if available
  if (stats.length > 3 && settings?.shippingSettings?.estimatedDelivery) {
    stats[3] = {
      ...stats[3],
      value: settings.shippingSettings.estimatedDelivery,
    };
  }

  const AnimatedNumber = ({
    value,
    suffix = "",
  }: {
    value: string;
    suffix?: string;
  }) => {
    const [displayValue, setDisplayValue] = useState("0");

    useEffect(() => {
      // Always show the value, with animation if visible
      const numericValue = parseFloat(value.replace(/[^0-9.]/g, ""));
      if (isNaN(numericValue)) {
        setDisplayValue(value);
        return;
      }

      if (!isVisible) {
        // If not visible yet, show the final value immediately
        setDisplayValue(value);
        return;
      }

      // Animate from 0 to final value
      let start = 0;
      const duration = 2000;
      const increment = numericValue / (duration / 16);

      const timer = setInterval(() => {
        start += increment;
        if (start >= numericValue) {
          setDisplayValue(value);
          clearInterval(timer);
        } else {
          setDisplayValue(Math.floor(start).toLocaleString("vi-VN"));
        }
      }, 16);

      return () => clearInterval(timer);
    }, [isVisible, value]);

    return (
      <span>
        {displayValue}
        {suffix}
      </span>
    );
  };

  return (
    <section id="stats-section" className="py-16 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold mb-4">
            Tại sao chọn NS Shop?
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Chúng tôi tự hào là điểm đến tin cậy cho những tín đồ thời trang
            trên toàn quốc
          </p>
        </div>

        <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <div
              key={index}
              className="text-center group hover:scale-105 transition-transform duration-300"
            >
              <div className="flex justify-center mb-4">
                <div className="p-4 bg-white dark:bg-card rounded-2xl shadow-lg group-hover:shadow-xl transition-shadow">
                  {stat.icon}
                </div>
              </div>
              <div className="text-3xl lg:text-4xl font-bold mb-2">
                <AnimatedNumber value={stat.value} suffix={stat.suffix} />
              </div>
              <div className="text-muted-foreground font-medium">
                {stat.label}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
