"use client";

import Link from "next/link";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Calendar, User, ArrowRight, Clock, Eye, Loader2 } from "lucide-react";
import { formatDate } from "@/lib/utils";
import { useFeaturedPosts } from "@/hooks/use-posts";

export function BlogSection() {
  const { posts, loading, error } = useFeaturedPosts(3);
  return (
    <section className="py-16 lg:py-24 bg-muted/30">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold mb-4">
            Blog <span className="text-fashion-600">thời trang</span>
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            <PERSON><PERSON><PERSON> nh<PERSON>t những xu hướng mới nhất, mẹo phối đồ và kiến thức thời
            trang từ các chuyên gia
          </p>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="flex justify-center items-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-fashion-500" />
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="text-center py-12">
            <p className="text-muted-foreground">
              Có lỗi xảy ra khi tải bài viết
            </p>
          </div>
        )}

        {/* Featured Post */}
        {!loading && !error && posts.length > 0 && posts[0].featured && (
          <div className="mb-12">
            {(() => {
              const featuredPost = posts[0];
              return (
                <Card className="overflow-hidden border-0 shadow-lg bg-white/80 backdrop-blur-sm">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
                    {/* Image */}
                    <div className="relative overflow-hidden bg-muted">
                      <div className="aspect-[16/10] lg:aspect-auto lg:h-full bg-gradient-to-br from-muted to-muted-foreground/10 flex items-center justify-center">
                        <div className="text-6xl font-bold text-muted-foreground/20">
                          {featuredPost.title.charAt(0)}
                        </div>
                      </div>
                      <Badge className="absolute top-4 left-4 bg-fashion-500 text-white">
                        Nổi bật
                      </Badge>
                    </div>

                    {/* Content */}
                    <CardContent className="p-8 flex flex-col justify-center">
                      <div className="space-y-4">
                        {/* Category */}
                        <Badge variant="secondary" className="w-fit">
                          {featuredPost.category}
                        </Badge>

                        {/* Title */}
                        <Link href={`/blog/${featuredPost.slug}`}>
                          <h3 className="text-2xl lg:text-3xl font-bold hover:text-fashion-600 transition-colors line-clamp-2">
                            {featuredPost.title}
                          </h3>
                        </Link>

                        {/* Excerpt */}
                        <p className="text-muted-foreground leading-relaxed line-clamp-3">
                          {featuredPost.excerpt}
                        </p>

                        {/* Meta */}
                        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                          <div className="flex items-center space-x-1">
                            <User className="h-4 w-4" />
                            <span>{featuredPost.author}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-4 w-4" />
                            <span>{formatDate(featuredPost.publishedAt)}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Clock className="h-4 w-4" />
                            <span>{featuredPost.readTime} phút đọc</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Eye className="h-4 w-4" />
                            <span>{featuredPost.views}</span>
                          </div>
                        </div>

                        {/* Tags */}
                        <div className="flex flex-wrap gap-2">
                          {featuredPost.tags.map((tag) => (
                            <Badge
                              key={tag}
                              variant="outline"
                              className="text-xs"
                            >
                              {tag}
                            </Badge>
                          ))}
                        </div>

                        {/* Read More */}
                        <Button
                          asChild
                          variant="fashion"
                          className="w-fit group"
                        >
                          <Link href={`/blog/${featuredPost.slug}`}>
                            Đọc tiếp
                            <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                          </Link>
                        </Button>
                      </div>
                    </CardContent>
                  </div>
                </Card>
              );
            })()}
          </div>
        )}

        {/* Other Posts */}
        {!loading && !error && posts.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 lg:gap-8">
            {posts
              .filter((post) => !post.featured)
              .slice(0, 2)
              .map((post) => (
                <Card
                  key={post.id}
                  className="group overflow-hidden border-0 shadow-sm hover:shadow-lg transition-all duration-300 bg-white/80 backdrop-blur-sm"
                >
                  <CardContent className="p-0">
                    {/* Image */}
                    <div className="relative overflow-hidden bg-muted">
                      <div className="aspect-[16/10] bg-gradient-to-br from-muted to-muted-foreground/10 flex items-center justify-center">
                        <div className="text-4xl font-bold text-muted-foreground/20">
                          {post.title.charAt(0)}
                        </div>
                      </div>
                      <Badge className="absolute top-3 left-3 bg-white/90 text-foreground">
                        {post.category}
                      </Badge>
                    </div>

                    {/* Content */}
                    <div className="p-6">
                      <div className="space-y-3">
                        {/* Title */}
                        <Link href={`/blog/${post.slug}`}>
                          <h3 className="font-bold text-lg group-hover:text-fashion-600 transition-colors line-clamp-2">
                            {post.title}
                          </h3>
                        </Link>

                        {/* Excerpt */}
                        <p className="text-sm text-muted-foreground line-clamp-3">
                          {post.excerpt}
                        </p>

                        {/* Meta */}
                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <div className="flex items-center space-x-2">
                            <div className="flex items-center space-x-1">
                              <User className="h-3 w-3" />
                              <span>{post.author}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Clock className="h-3 w-3" />
                              <span>{post.readTime}p</span>
                            </div>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Eye className="h-3 w-3" />
                            <span>{post.views}</span>
                          </div>
                        </div>

                        {/* Tags */}
                        <div className="flex flex-wrap gap-1">
                          {post.tags.slice(0, 2).map((tag) => (
                            <Badge
                              key={tag}
                              variant="outline"
                              className="text-xs"
                            >
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
          </div>
        )}

        {/* View All Button */}
        <div className="text-center mt-12">
          <Button asChild variant="outline" size="lg" className="group">
            <Link href="/blog">
              Xem tất cả bài viết
              <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
}
